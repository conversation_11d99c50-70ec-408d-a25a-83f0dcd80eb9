import{b as c,h as f,a as _,c as d,d as h,t as l,E as u,f as m,n as v,g,i as o,j as y}from"./UXlYzocm.js";function E(r,n,...s){var t=r,e=v,a;c(()=>{e!==(e=n())&&(a&&(g(a),a=null),a=m(()=>e(t,...s)))},u),f&&(t=o)}function R(r){return(n,...s)=>{var t=r(...s),e;if(f)e=o,_();else{var a=t.render().trim(),p=d(a);e=y(p),n.before(e)}const i=t.setup?.(e);h(e,e),typeof i=="function"&&l(i)}}export{R as c,E as s};
