export default class MatchingCard extends Phaser.GameObjects.Container {
  private cardImage: Phaser.GameObjects.Image;
  private cardBackground: Phaser.GameObjects.Image;
  private isCorrect: boolean = false;
  private isSelected: boolean = false;
  private cardSize: number;

  constructor(scene: Phaser.Scene, x: number, y: number, size: number = 120) {
    super(scene, x, y);
    
    this.cardSize = size;
    
    // Create card background using the SVG image
    this.cardBackground = scene.add.image(0, 0, 'card_bg')
      .setOrigin(0.5);
    
    // Scale the background to fit the desired card size
    this.cardBackground.displayWidth = size;
    this.cardBackground.displayHeight = size;
    this.add(this.cardBackground);
    
    // Create card image (initially empty)
    this.cardImage = scene.add.image(0, 0, '');
    this.cardImage.setScale(0); // Start with scale 0 for animation
    this.add(this.cardImage);
    
    // Configure the container
    this.setSize(size, size);
    this.setInteractive({ useHandCursor: true });
    
    // Hover effects
    this.on('pointerover', this.onPointerOver, this);
    this.on('pointerout', this.onPointerOut, this);
    
    // Add to scene
    scene.add.existing(this);
  }
  
  /**
   * Set the tint color for the card image
   */
  public setTint(color: number): void {
    this.cardImage.setTint(color);
  }
  
  /**
   * Set the card's image
   */
  public setCardImage(imageKey: string): void {
    // Store the image key in the card's data for reference
    this.setData('imageKey', imageKey);
    
    if (!imageKey || imageKey === '') {
      // Reset to empty state if no image key provided
      this.cardImage.setTexture(''); 
      this.cardImage.setVisible(false);
      return;
    }

    // Try to set the texture, falling back gracefully if texture doesn't exist
    try {
      this.cardImage.setTexture(imageKey);
      this.cardImage.setVisible(true);
      this.cardImage.setScale(0); // Reset scale for animation
    
      // Ensure the image fits within the card while maintaining aspect ratio
      this.scene.time.delayedCall(10, () => {
        // Get current dimensions
        const cardWidth = this.cardSize * 0.7; // Use 70% of card width
        const cardHeight = this.cardSize * 0.7; // Use 70% of card height
        const imgWidth = this.cardImage.width;
        const imgHeight = this.cardImage.height;
        
        // Calculate aspect ratios
        const cardRatio = cardWidth / cardHeight;
        const imgRatio = imgWidth / imgHeight;
        
        // Determine scale based on aspect ratios
        let scale = 1;
        if (imgRatio > cardRatio) {
          // Image is wider than card - scale based on width
          scale = cardWidth / imgWidth;
        } else {
          // Image is taller than card - scale based on height
          scale = cardHeight / imgHeight;
        }
        
        // Store the target scale for animation
        this.cardImage.setData('targetScale', scale);
      });
    } catch (error) {
      console.error(`Failed to set texture for ${imageKey}`, error);
      // Fall back to empty texture
      this.cardImage.setTexture('');
      this.cardImage.setVisible(false);
    }
  }
  
  /**
   * Animate the card image appearing
   */
  public animateCardImage(delay: number = 0): void {
    // Wait a tiny bit to ensure target scale is calculated
    this.scene.time.delayedCall(20, () => {
      // Get target scale (default to 1 if not set)
      const targetScale = this.cardImage.getData('targetScale') || 1;
      
      this.scene.tweens.add({
        targets: this.cardImage,
        scaleX: targetScale,
        scaleY: targetScale,
        duration: 300,
        ease: 'Sine.easeInOut',
        delay: delay
      });
    });
  }
  
  /**
   * Mark this card as the correct answer
   */
  public setCorrect(isCorrect: boolean): void {
    this.isCorrect = isCorrect;
  }
  
  /**
   * Check if this card is the correct answer
   */
  public getIsCorrect(): boolean {
    return this.isCorrect;
  }
  
  /**
   * Mark the card as selected (right or wrong)
   */
  public markSelected(isRight: boolean): void {
    if (this.isSelected) return;
    
    this.isSelected = true;
    
    // Set the card background based on right/wrong
    if (isRight) {
      // Green for correct - swap to correct background
      this.cardBackground.setTexture('card_correct_bg');
      
      // Keep the same size
      this.cardBackground.displayWidth = this.cardSize;
      this.cardBackground.displayHeight = this.cardSize;
      
      // Tint the card image green
      this.cardImage.setTint(0x00ff00);
    } else {
      // Red for wrong - swap to incorrect background
      this.cardBackground.setTexture('card_incorrect_bg');
      
      // Keep the same size
      this.cardBackground.displayWidth = this.cardSize;
      this.cardBackground.displayHeight = this.cardSize;
      
      // Tint the card image red
      this.cardImage.setTint(0xff0000);
      
      // Shake the card if wrong
      this.scene.tweens.add({
        targets: this,
        x: this.x + 10,
        duration: 40,
        yoyo: true,
        repeat: 5
      });
    }
  }
  
  /**
   * Reset the card's selection state
   */
  public resetSelection(): void {
    this.isSelected = false;
    
    // Reset to default card background
    this.cardBackground.setTexture('card_bg');
    
    // Ensure correct size
    this.cardBackground.displayWidth = this.cardSize;
    this.cardBackground.displayHeight = this.cardSize;
  }
  
  /**
   * Handle pointer over event
   */
  private onPointerOver(): void {
    if (this.isSelected) return;
    
    // Scale up slightly
    this.scene.tweens.add({
      targets: this,
      scaleX: 1.05,
      scaleY: 1.05,
      duration: 100,
      ease: 'Sine.easeOut'
    });
  }
  
  /**
   * Handle pointer out event
   */
  private onPointerOut(): void {
    if (this.isSelected) return;
    
    // Scale back to normal
    this.scene.tweens.add({
      targets: this,
      scaleX: 1,
      scaleY: 1,
      duration: 100,
      ease: 'Sine.easeOut'
    });
  }
  
  /**
   * Clean up resources when destroying the card
   */
  public destroy(fromScene?: boolean): void {
    super.destroy(fromScene);
  }
  
  /**
   * Get the card background image
   */
  public getCardBackground(): Phaser.GameObjects.Image {
    return this.cardBackground;
  }
}