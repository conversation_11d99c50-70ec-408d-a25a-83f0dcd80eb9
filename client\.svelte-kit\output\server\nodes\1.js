

export const index = 1;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/fallbacks/error.svelte.js')).default;
export const imports = ["_app/immutable/nodes/1.um0fz6Fw.js","_app/immutable/chunks/DsnmJJEf.js","_app/immutable/chunks/BzjXworP.js","_app/immutable/chunks/UXlYzocm.js","_app/immutable/chunks/AD_Ai2mn.js","_app/immutable/chunks/ByeBDOvZ.js","_app/immutable/chunks/CcjTyetE.js","_app/immutable/chunks/D8RLQ4bx.js","_app/immutable/chunks/DdHBXBi3.js"];
export const stylesheets = [];
export const fonts = [];
