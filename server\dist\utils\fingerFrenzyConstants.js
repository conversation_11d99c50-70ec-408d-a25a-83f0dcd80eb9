/**
 * Finger Frenzy Game Constants
 * Configuration constants for the Finger Frenzy game
 */
export const FINGER_FRENZY_CONFIG = {
    // Grid configuration
    GRID_SIZE: 4,
    INITIAL_ACTIVE_BLOCKS: 3,
    // Scoring system
    SCORE_TIERS: {
        FAST: 5, // < 500ms
        MEDIUM_FAST: 4, // < 1000ms
        MEDIUM: 3, // < 1500ms
        MEDIUM_SLOW: 2, // < 2000ms
        SLOW: 1 // >= 2000ms
    },
    // Reaction time thresholds (in milliseconds)
    SCORE_TIER_THRESHOLDS: {
        FAST: 500,
        MEDIUM_FAST: 1000,
        MEDIUM: 1500,
        MEDIUM_SLOW: 2000
    },
    // Game penalties and limits
    WRONG_CLICK_PENALTY: 5,
    MAX_LIVES: 3
};
