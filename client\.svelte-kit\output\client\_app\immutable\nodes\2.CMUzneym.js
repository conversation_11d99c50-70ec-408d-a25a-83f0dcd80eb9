import"../chunks/DsnmJJEf.js";import"../chunks/BzjXworP.js";import{G as m,K as n,$ as N,O as d,M as r,N as e,B as i,P as u,J as w}from"../chunks/UXlYzocm.js";import{h as k,s as c}from"../chunks/AD_Ai2mn.js";import{i as B}from"../chunks/DLbaRTQ9.js";import{e as C,i as T,s as F,a as G}from"../chunks/nPv1uoDA.js";var S=m('<meta name="description" content="Select from our collection of exciting mini-games"/>'),j=m('<a><div class="flex items-center justify-between mb-4"><div class="px-3 py-1 bg-white/20 rounded-full text-sm font-medium text-white">Available</div></div> <h2 class="text-2xl font-bold text-white mb-2 group-hover:text-yellow-200 transition-colors"> </h2> <p class="text-white/80 text-sm leading-relaxed mb-4"> </p> <div class="flex items-center text-white/60 text-sm"><svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg> Play Now</div></a>'),q=m('<div class="block h-full p-6 rounded-xl bg-gradient-to-br from-gray-600 to-gray-700 shadow-lg border border-white/10 opacity-60 cursor-not-allowed"><div class="flex items-center justify-between mb-4"><div class="px-3 py-1 bg-gray-500/50 rounded-full text-sm font-medium text-gray-300">Coming Soon</div></div> <h2 class="text-2xl font-bold text-gray-300 mb-2"> </h2> <p class="text-gray-400 text-sm leading-relaxed mb-4"> </p> <div class="flex items-center text-gray-500 text-sm"><svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg> Not Available</div></div>'),A=m('<div class="game-card group svelte-1e50pho"><!></div>'),P=m('<div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900"><header class="text-center py-12 px-4"><h1 class="text-5xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">TicTaps Games</h1></header> <main class="container mx-auto px-4 pb-12"><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6 max-w-4xl mx-auto"></div></main></div>');function E(y){const _=[{id:"finger-frenzy",name:"Finger Frenzy",description:"Fast-paced tapping game",color:"from-red-500 to-orange-500",available:!0},{id:"bingo",name:"Bingo",description:"Classic bingo game",color:"from-blue-500 to-purple-500",available:!1},{id:"matching-mayhem",name:"Matching Mayhem",description:"Memory matching game",color:"from-green-500 to-teal-500",available:!1},{id:"number-sequence",name:"Number Sequence",description:"Number pattern recognition",color:"from-purple-500 to-pink-500",available:!1}];var v=P();k(p=>{var t=S();N.title="TicTaps Games",n(p,t)});var f=d(r(v),2),h=r(f);C(h,5,()=>_,T,(p,t)=>{var x=A(),M=r(x);{var z=l=>{var a=j(),o=d(r(a),2),g=r(o,!0);e(o);var s=d(o,2),b=r(s,!0);e(s),u(2),e(a),w(()=>{F(a,"href",`/game/${i(t).id??""}`),G(a,1,`block h-full p-6 rounded-xl bg-gradient-to-br ${i(t).color??""} shadow-lg hover:shadow-2xl transform hover:scale-105 transition-all duration-300 border border-white/10`,"svelte-1e50pho"),c(g,i(t).name),c(b,i(t).description)}),n(l,a)},L=l=>{var a=q(),o=d(r(a),2),g=r(o,!0);e(o);var s=d(o,2),b=r(s,!0);e(s),u(2),e(a),w(()=>{c(g,i(t).name),c(b,i(t).description)}),n(l,a)};B(M,l=>{i(t).available?l(z):l(L,!1)})}e(x),n(p,x)}),e(h),e(f),e(v),n(y,v)}export{E as component};
