import { logger } from '../../utils/logger';
import { FINGER_FRENZY_CONFIG } from '../../utils/fingerFrenzyConstants';
import { GAME_TYPES } from '../../utils/constants';
export default class FingerFrenzyController {
    constructor(gameService) {
        this.gameData = new Map();
        this.gameService = gameService;
    }
    /**
     * Initialize and start a new Finger Frenzy game session
     */
    initializeAndStartGame(roomId, socket) {
        // Check if room already has a game state
        let gameState = this.gameService.getGameState(roomId);
        if (gameState) {
            // Game state exists, check if we can start
            if (gameState.status === 'active') {
                return { success: false, message: 'Game is already active' };
            }
            if (gameState.status === 'ended') {
                return { success: false, message: 'Game session has ended - no restarts allowed' };
            }
        }
        else {
            // Create new game state
            gameState = this.gameService.createGameState(roomId, GAME_TYPES.FINGER_FRENZY, FINGER_FRENZY_CONFIG.MAX_LIVES);
        }
        // Start the game using GameService
        const startSuccess = this.gameService.startGame(roomId, socket);
        if (!startSuccess) {
            return { success: false, message: 'Failed to start game' };
        }
        // Create and initialize game data with grid
        const grid = this.createGrid();
        const gameData = {
            grid,
            activeBlockCount: 0
        };
        this.gameData.set(roomId, gameData);
        // Activate initial blocks for game start
        this.activateInitialBlocks(roomId);
        logger.info(`Finger Frenzy game initialized and started for room ${roomId}`);
        return { success: true, gameState };
    }
    /**
     * End the game session
     */
    endGame(roomId, reason = 'manual') {
        const gameState = this.gameService.getGameState(roomId);
        if (!gameState || gameState.status !== 'active') {
            logger.warn(`Cannot end game: no active game in room ${roomId}`);
            return false;
        }
        // End game using GameService
        const endReason = reason === 'timeout' ? 'timeout' : reason === 'no_lives' ? 'completed' : 'forfeit';
        this.gameService.endGame(roomId, endReason);
        // Deactivate all blocks
        this.deactivateAllBlocks(roomId);
        // Clean up game state
        this.cleanupGame(roomId);
        logger.info(`Finger Frenzy game ended in room ${roomId}, reason: ${reason}, final score: ${gameState.score}`);
        return true;
    }
    /**
     * Create a 4x4 grid of blocks
     */
    createGrid() {
        const grid = [];
        for (let row = 0; row < FINGER_FRENZY_CONFIG.GRID_SIZE; row++) {
            for (let col = 0; col < FINGER_FRENZY_CONFIG.GRID_SIZE; col++) {
                const index = row * FINGER_FRENZY_CONFIG.GRID_SIZE + col;
                const id = `block_${row}_${col}`;
                grid.push({
                    id,
                    row,
                    col,
                    isActive: false,
                    index
                });
            }
        }
        return grid;
    }
    /**
     * Activate initial blocks when game starts
     */
    activateInitialBlocks(roomId) {
        const gameData = this.gameData.get(roomId);
        if (!gameData) {
            return;
        }
        // Activate 3 specific initial positions (matching client logic)
        const initialPositions = [
            { row: 1, col: 2 },
            { row: 2, col: 3 },
            { row: 0, col: 1 }
        ];
        gameData.activeBlockCount = 0;
        for (const pos of initialPositions) {
            const index = pos.row * FINGER_FRENZY_CONFIG.GRID_SIZE + pos.col;
            if (index < gameData.grid.length) {
                gameData.grid[index].isActive = true;
                gameData.activeBlockCount++;
            }
        }
        logger.info(`Activated ${gameData.activeBlockCount} initial blocks for room ${roomId}`);
    }
    /**
     * Handle a tile tap by ID
     */
    handleTileTap(roomId, tileId, reactionTime) {
        const gameState = this.gameService.getGameState(roomId);
        const gameData = this.gameData.get(roomId);
        if (!gameState || !gameData) {
            return {
                newBlock: null,
                success: false,
                isCorrect: false,
                points: 0,
                newScore: 0,
                newLives: 0,
                gameEnded: false,
                message: 'Game state not found'
            };
        }
        if (gameState.status !== 'active') {
            return {
                newBlock: null,
                success: false,
                isCorrect: false,
                points: 0,
                newScore: gameState.score,
                newLives: gameState.lives,
                gameEnded: false,
                message: 'Game is not active'
            };
        }
        // Find the block by ID
        const block = gameData.grid.find(b => b.id === tileId);
        if (!block) {
            return {
                newBlock: null,
                success: false,
                isCorrect: false,
                points: 0,
                newScore: gameState.score,
                newLives: gameState.lives,
                gameEnded: false,
                message: 'Invalid tile ID'
            };
        }
        // Check if the tile is active (correct tap) or inactive (wrong tap)
        if (block.isActive) {
            // CORRECT TAP
            // Validate reaction time (should be reasonable)
            if (reactionTime < 0 || reactionTime > 10000) {
                return {
                    newBlock: null,
                    success: false,
                    isCorrect: false,
                    points: 0,
                    newScore: gameState.score,
                    newLives: gameState.lives,
                    gameEnded: false,
                    message: 'Invalid reaction time'
                };
            }
            // Calculate points based on reaction time
            const points = this.calculatePoints(reactionTime);
            // Update score using GameService
            this.gameService.updateScore(roomId, points, "add");
            // Deactivate the clicked block
            block.isActive = false;
            gameData.activeBlockCount--;
            // Activate a new random block (excluding the clicked one)
            const newBlock = this.activateRandomBlock(roomId, block.index);
            logger.info(`Correct tap in room ${roomId}: tile ${tileId}, reaction time ${reactionTime}ms, points ${points}, new score ${gameState.score}`);
            return {
                newBlock,
                success: true,
                isCorrect: true,
                points,
                newScore: gameState.score,
                newLives: gameState.lives,
                gameEnded: false,
            };
        }
        else {
            // WRONG TAP
            // Apply penalty
            const penalty = FINGER_FRENZY_CONFIG.WRONG_CLICK_PENALTY;
            this.gameService.updateScore(roomId, penalty, "subtract");
            // Deduct life using GameService
            const livesResult = this.gameService.deductLife(roomId);
            // Check if game should end due to no lives
            if (livesResult.gameEnded) {
                this.endGame(roomId, 'no_lives');
            }
            logger.info(`Wrong tap in room ${roomId}: tile ${tileId}, penalty ${penalty}, new score ${gameState.score}, lives ${livesResult.newLives}, game ended: ${livesResult.gameEnded}`);
            return {
                newBlock: null,
                success: true,
                isCorrect: false,
                points: penalty,
                newScore: gameState.score,
                newLives: livesResult.newLives,
                gameEnded: livesResult.gameEnded
            };
        }
    }
    /**
     * Get all tile states for the room
     */
    getAllTileStates(roomId) {
        const gameData = this.gameData.get(roomId);
        if (!gameData) {
            return null;
        }
        return gameData.grid.reduce((states, block) => {
            states[block.id] = { isActive: block.isActive };
            return states;
        }, {});
    }
    /**
     * Calculate points based on reaction time
     */
    calculatePoints(reactionTime) {
        if (reactionTime < FINGER_FRENZY_CONFIG.SCORE_TIER_THRESHOLDS.FAST) {
            return FINGER_FRENZY_CONFIG.SCORE_TIERS.FAST;
        }
        if (reactionTime < FINGER_FRENZY_CONFIG.SCORE_TIER_THRESHOLDS.MEDIUM_FAST) {
            return FINGER_FRENZY_CONFIG.SCORE_TIERS.MEDIUM_FAST;
        }
        if (reactionTime < FINGER_FRENZY_CONFIG.SCORE_TIER_THRESHOLDS.MEDIUM) {
            return FINGER_FRENZY_CONFIG.SCORE_TIERS.MEDIUM;
        }
        if (reactionTime < FINGER_FRENZY_CONFIG.SCORE_TIER_THRESHOLDS.MEDIUM_SLOW) {
            return FINGER_FRENZY_CONFIG.SCORE_TIERS.MEDIUM_SLOW;
        }
        return FINGER_FRENZY_CONFIG.SCORE_TIERS.SLOW;
    }
    /**
     * Activate a random block (excluding specified block and already active blocks)
     */
    activateRandomBlock(roomId, excludeBlockIndex) {
        const gameState = this.gameService.getGameState(roomId);
        const gameData = this.gameData.get(roomId);
        if (!gameState || !gameData || gameState.status !== 'active') {
            return null;
        }
        // Don't activate more blocks if we already have the maximum
        if (gameData.activeBlockCount >= FINGER_FRENZY_CONFIG.INITIAL_ACTIVE_BLOCKS) {
            return null;
        }
        // Get list of available positions (exclude currently active blocks and the excluded block)
        const availableIndices = gameData.grid
            .map((block, index) => ({ block, index }))
            .filter(({ block, index }) => !block.isActive &&
            (excludeBlockIndex === undefined || index !== excludeBlockIndex))
            .map(({ index }) => index);
        // If we have available positions, activate one randomly
        if (availableIndices.length > 0) {
            const randomIndex = availableIndices[Math.floor(Math.random() * availableIndices.length)];
            const block = gameData.grid[randomIndex];
            block.isActive = true;
            gameData.activeBlockCount++;
            logger.info(`Activated random block ${randomIndex} in room ${roomId}, active count: ${gameData.activeBlockCount}`);
            return block;
        }
        return null;
    }
    /**
     * Get grid state for client synchronization
     */
    getGridState(roomId) {
        const gameData = this.gameData.get(roomId);
        if (!gameData) {
            return null;
        }
        return {
            blocks: gameData.grid.map((block) => ({ ...block })), // Return a copy
            activeCount: gameData.activeBlockCount
        };
    }
    /**
     * Deactivate all blocks (useful for game end or reset)
     */
    deactivateAllBlocks(roomId) {
        const gameData = this.gameData.get(roomId);
        if (!gameData) {
            return false;
        }
        gameData.grid.forEach(block => block.isActive = false);
        gameData.activeBlockCount = 0;
        logger.info(`Deactivated all blocks in room ${roomId}`);
        return true;
    }
    /**
     * Clean up game state when room is destroyed
     */
    cleanupGame(roomId) {
        const deleted = this.gameService.deleteGameState(roomId);
        this.gameData.delete(roomId);
        if (deleted) {
            logger.info(`Cleaned up Finger Frenzy game for room ${roomId}`);
        }
        return deleted;
    }
    /**
     * Setup socket event handlers for Finger Frenzy
     */
    setupSocketHandlers(io, socket) {
        // Generic game start event
        socket.on('start', (data) => {
            if (data.gameId === GAME_TYPES.FINGER_FRENZY) {
                this.handleGameStart(socket, data);
            }
        });
        // Generic game end event
        socket.on('end', (data) => {
            if (data.gameId === GAME_TYPES.FINGER_FRENZY) {
                this.handleGameEnd(io, socket, data);
            }
        });
        // Generic game action event (for game-specific actions)
        socket.on('action', (data) => {
            if (data.gameId === GAME_TYPES.FINGER_FRENZY) {
                this.handleGameAction(socket, data);
            }
        });
    }
    /**
     * Handle generic game action event
     */
    handleGameAction(socket, data) {
        const { roomId, gameId, action } = data;
        if (!roomId || !gameId || !action) {
            socket.emit('error', {
                message: 'Missing required data for game action (roomId, gameId, action)'
            });
            return;
        }
        try {
            // Process the game action using GameService
            const gameAction = {
                type: action.type,
                data: action.data,
                timestamp: Date.now()
            };
            const success = this.gameService.processGameAction(roomId, gameAction);
            if (success) {
                // Handle specific action types for Finger Frenzy
                switch (action.type) {
                    case 'tile_tap':
                        this.handleTileTapAction(socket, data);
                        break;
                    default:
                        socket.emit('error', {
                            message: `Unknown action type: ${action.type}`
                        });
                }
            }
            else {
                socket.emit('error', {
                    message: 'Failed to process game action'
                });
            }
        }
        catch (error) {
            logger.error(`Error processing game action in room ${roomId}:`, error);
            socket.emit('error', {
                message: 'Internal server error'
            });
        }
    }
    /**
     * Handle game start event
     */
    handleGameStart(socket, data) {
        const { roomId, gameId } = data;
        if (!roomId || !gameId) {
            socket.emit('game_error', {
                message: 'Missing roomId, or gameId'
            });
            return;
        }
        try {
            // Initialize and start the game in one step
            const result = this.initializeAndStartGame(roomId, socket);
            if (result.success && result.gameState) {
                // Get initial grid state
                const gridState = this.getGridState(roomId);
                socket.emit('started', {
                    gameState: {
                        score: result.gameState.score,
                        lives: result.gameState.lives,
                        isActive: result.gameState.status === 'active',
                        startTime: result.gameState.startTime
                    },
                    gridState,
                    message: 'Game started!'
                });
                logger.info(`${gameId} game started in room ${roomId} `);
            }
            else {
                socket.emit('error', {
                    message: result.message || 'Failed to start game'
                });
            }
        }
        catch (error) {
            logger.error(`Error starting ${gameId} game in room ${roomId}:`, error);
            socket.emit('error', {
                message: 'Internal server error'
            });
        }
    }
    /**
     * Handle generic game end event
     */
    handleGameEnd(io, socket, data) {
        const { roomId, reason = 'manual', gameId } = data;
        if (!roomId || !gameId) {
            socket.emit('error', {
                message: 'Missing roomId, or gameId'
            });
            return;
        }
        try {
            const gameState = this.gameService.getGameState(roomId);
            if (!gameState) {
                socket.emit('error', {
                    message: 'Game state not found'
                });
                return;
            }
            const success = this.endGame(roomId, reason);
            if (success) {
                // Broadcast game end to all players in room
                io.to(roomId).emit('ended', {
                    reason,
                    finalScore: gameState.score,
                });
                logger.info(`${gameId} game ended in room ${roomId}, reason: ${reason}`);
            }
            else {
                socket.emit('error', {
                    message: 'Failed to end game'
                });
            }
        }
        catch (error) {
            logger.error(`Error ending ${gameId} game in room ${roomId}:`, error);
            socket.emit('error', {
                message: 'Internal server error'
            });
        }
    }
    /**
     * Handle tile tap action
     */
    handleTileTapAction(socket, data) {
        const { roomId, gameId, action } = data;
        const { tileId, reactionTime, clickTime } = action.data;
        if (!roomId || !tileId || !gameId) {
            socket.emit('error', {
                message: 'Missing required data for tile tap action'
            });
            return;
        }
        try {
            // Validate the tap timing
            const currentTime = Date.now();
            const timeDiff = Math.abs(currentTime - (clickTime || currentTime));
            if (timeDiff > 5000) { // 5 seconds tolerance
                socket.emit('error', {
                    message: 'Tap time too far from current time'
                });
                return;
            }
            // Process the tile tap
            const result = this.handleTileTap(roomId, tileId, reactionTime || 0);
            if (result.success) {
                // Get updated grid state and tile states
                const gridState = this.getGridState(roomId);
                const tileStates = this.getAllTileStates(roomId);
                // Broadcast the action result to all players in room
                socket.emit('action_result', {
                    actionType: 'tile_tap',
                    data: {
                        tileId,
                        isCorrect: result.isCorrect,
                        points: result.points,
                        newScore: result.newScore,
                        newLives: result.newLives,
                        gameEnded: result.gameEnded,
                        gridState,
                        tileStates,
                    }
                });
                // If game ended, broadcast game end event
                if (result.gameEnded) {
                    socket.emit('ended', {
                        reason: 'no_lives',
                        finalScore: result.newScore
                    });
                }
                logger.info(`Tile tap processed for room ${roomId}, tile ${tileId}, correct: ${result.isCorrect}, points: ${result.points}`);
            }
            else {
                socket.emit('game_error', {
                    message: result.message || 'Failed to process tile tap'
                });
            }
        }
        catch (error) {
            logger.error(`Error processing tile tap in room ${roomId}:`, error);
            socket.emit('game_error', {
                message: 'Internal server error'
            });
        }
    }
}
