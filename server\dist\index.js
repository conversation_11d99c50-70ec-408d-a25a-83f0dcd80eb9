import express from 'express';
import http from 'http';
import { Server } from 'socket.io';
import cors from 'cors';
import { setupSocketHandlers } from './sockets/index.js';
import * as dotenv from 'dotenv';
// Load environment variables
dotenv.config();
const app = express();
const server = http.createServer(app);
const CORS_ORIGIN = process.env.CORS_ORIGIN || "https://games.tictaps.dev";
console.log(CORS_ORIGIN);
// Allowed origins (add more as needed)
const allowedOrigins = [
    "http://localhost:5173",
    CORS_ORIGIN
];
// CORS configuration for Express routes
app.use(cors({
    origin: (origin, callback) => {
        if (!origin || allowedOrigins.includes(origin)) {
            callback(null, true);
        }
        else {
            callback(new Error(`CORS blocked for origin: ${origin}`));
        }
    },
    credentials: true
}));
app.use(express.json());
// CORS configuration for Socket.IO
const io = new Server(server, {
    cors: {
        origin: allowedOrigins,
        methods: ["GET", "POST"],
        credentials: true
    }
});
setupSocketHandlers(io);
// Start server
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
    console.log(`TicTaps Games Server listening on http://localhost:${PORT}`);
});
