import TicTapsConnector from '../utils/TicTapsConnector';
import GameConfig from '../config/GameConfig';

export default class GameStartScene extends Phaser.Scene {
  private ticTaps!: TicTapsConnector;
  private startButton!: Phaser.GameObjects.Image;
  private isStarting: boolean = false;

  constructor() {
    super('GameStartScene');
  }

  create(): void {
    const { width, height } = this.cameras.main;

    // Initialize TicTaps connector
    this.ticTaps = new TicTapsConnector();

    // Background - fallback to black if asset missing
    if (this.textures.exists('game_background')) {
      this.add.image(0, 0, 'game_background')
        .setOrigin(0, 0)
        .setDisplaySize(width, height);
    } else {
      // Fallback: create a dark background
      this.cameras.main.setBackgroundColor('#000000');
      console.warn('game_background asset missing, using fallback');
    }

    // Game Title - with fallback
    let gameTitle: Phaser.GameObjects.Image | Phaser.GameObjects.Text;
    let titleScale: number;
    
    if (this.textures.exists('game_name')) {
      gameTitle = this.add.image(
        width / 2,
        height * 0.25,
        'game_name'
      ).setOrigin(0.5);
      
      titleScale = 
      // (width * 0.6) / (gameTitle as Phaser.GameObjects.Image).width;
      Math.min((width * 0.7) / gameTitle.width, 0.8); // Limit max scale to 0.8

      gameTitle.setScale(titleScale);
    } else {
      // Fallback: create text title
      gameTitle = this.add.text(
        width / 2,
        height * 0.25,
        'NUMBERS GAME',
        {
          fontFamily: 'NeorisTrialBold, Arial',
          fontSize: '48px',
          fontStyle: 'bold',
          color: '#ffffff'
        }
      ).setOrigin(0.5);
      titleScale = 1;
      console.warn('game_name asset missing, using fallback text');
    }

    // Add pulse animation to title
    this.tweens.add({
      targets: gameTitle,
      scaleX: titleScale * 1.02,
      scaleY: titleScale * 1.02,
      duration: 1500,
      yoyo: true,
      repeat: -1,
      ease: 'Sine.easeInOut'
    });

    // Start Button - with fallback
    let buttonScale: number;
    let startText: Phaser.GameObjects.Image | Phaser.GameObjects.Text;
    
    if (this.textures.exists('button_bg')) {
      this.startButton = this.add.image(
        width / 2,
        height * 0.6,
        'button_bg'
      ).setOrigin(0.5);
      
      buttonScale = 
      //(width * 0.6) / this.startButton.width;
      Math.min((width * 0.6) / this.startButton.width, 0.4); // Limit max scale to 1.0

      this.startButton.setScale(buttonScale);
      
      // Start Text on button
      if (this.textures.exists('game_start')) {
        startText = this.add.image(
          this.startButton.x,
          this.startButton.y - 5,
          'game_start'
        ).setOrigin(0.5);
        
        const textScale = (this.startButton.displayWidth * 0.6) / (startText as Phaser.GameObjects.Image).width;
        startText.setScale(textScale);
      } else {
        startText = this.add.text(
          this.startButton.x,
          this.startButton.y,
          'START',
          {
            fontFamily: 'NeorisTrialBold, Arial',
            fontSize: '32px',
            fontStyle: 'bold',
            color: '#ffffff'
          }
        ).setOrigin(0.5);
        console.warn('game_start asset missing, using fallback text');
      }
    } else {
      // Fallback: create a simple button
      const buttonBg = this.add.graphics();
      buttonBg.fillStyle(0x007ACC, 1);
      buttonBg.lineStyle(2, 0x00ccff, 1);
      buttonBg.fillRoundedRect(-120, -40, 240, 80, 20);
      buttonBg.strokeRoundedRect(-120, -40, 240, 80, 20);
      
      this.startButton = this.add.container(
        width / 2,
        height * 0.6,
        [buttonBg]
      ) as any;
      
      buttonScale = 1;
      
      startText = this.add.text(
        this.startButton.x,
        this.startButton.y,
        'START GAME',
        {
          fontFamily: 'NeorisTrialBold, Arial',
          fontSize: '32px',
          fontStyle: 'bold',
          color: '#ffffff'
        }
      ).setOrigin(0.5);
      
      console.warn('button_bg asset missing, using fallback button');
    }

    // Make button interactive
    this.startButton.setInteractive({ useHandCursor: true });

    // Add hover effects
    this.startButton.on('pointerover', () => {
      this.tweens.add({
        targets: [this.startButton, startText],
        scaleX: buttonScale * 1.05,
        scaleY: buttonScale * 1.05,
        duration: 150,
        ease: 'Sine.easeOut'
      });
    });

    this.startButton.on('pointerout', () => {
      this.tweens.add({
        targets: [this.startButton, startText],
        scaleX: buttonScale,
        scaleY: buttonScale,
        duration: 150,
        ease: 'Sine.easeOut'
      });
    });

    // Add click animation
    this.startButton.on('pointerdown', () => {
      // Play sound if available
      try {
        if (this.sound.get('countdown')) {
          this.sound.play('countdown', { volume: GameConfig.SOUND_VOLUME });
        }
      } catch (error) {
        console.warn('Sound playback failed:', error);
      }

      this.tweens.add({
        targets: [this.startButton, startText],
        scaleX: buttonScale * 0.95,
        scaleY: buttonScale * 0.95,
        duration: GameConfig.FLASH_DURATION,
        yoyo: true,
        onComplete: () => this.startGameCountdown(width, height)
      });
    });


  }

  private startGameCountdown( width: number, height: number): void {
    if (this.isStarting) return;
    this.isStarting = true;

    // Create a white flash effect that covers the entire screen
    const flash = this.add.rectangle(
      width / 2, 
      height / 2,
      width,
      height,
      0xffffff
    ).setAlpha(0).setOrigin(0.5);

    // Make sure flash is on top of everything
    flash.setDepth(1000);

    // Flash in
    this.tweens.add({
      targets: flash,
      alpha: 0.8,
      duration: GameConfig.FLASH_DURATION,
      ease: 'Sine.easeOut',
      onComplete: () => {
        // Play transition sound if available
        if (this.sound.get('go')) {
          this.sound.play('go', { volume: GameConfig.SOUND_VOLUME });
        }

        // Hold the flash briefly then fade it out
        this.tweens.add({
          targets: flash,
          alpha: 0,
          delay: GameConfig.TRANSITION_DELAY,
          duration: GameConfig.TRANSITION_FADE_DURATION,
          ease: 'Sine.easeIn',
          onComplete: () => {
          // Notify TicTaps that the game is ready when start is clicked
          this.ticTaps.notifyGameReady();
          
          // Start the game scene
          this.scene.start('GameScene');
          }
        });
      }
    });
  }
}
