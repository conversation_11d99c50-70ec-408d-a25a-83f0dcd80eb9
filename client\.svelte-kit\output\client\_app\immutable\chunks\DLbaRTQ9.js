import{b as I,h as o,a as N,E as R,r as x,H as D,k as F,s as S,l as p,m as q,f as b,o as C,U as H,p as L,q as U,u as O,i as P}from"./UXlYzocm.js";function Z(E,T,g=!1){o&&N();var r=E,s=null,t=null,e=H,k=g?R:0,l=!1;const y=(n,a=!0)=>{l=!0,d(a,n)};var f=null;function _(){f!==null&&(f.lastChild.remove(),r.before(f),f=null);var n=e?s:t,a=e?t:s;n&&U(n),a&&O(a,()=>{e?t=null:s=null})}const d=(n,a)=>{if(e===(e=n))return;let u=!1;if(o){const A=x(r)===D;!!e===A&&(r=F(),S(r),p(!1),u=!0)}var v=L(),i=r;if(v&&(f=document.createDocumentFragment(),f.append(i=q())),e?s??=a&&b(()=>a(i)):t??=a&&b(()=>a(i)),v){var c=C,h=e?s:t,m=e?t:s;h&&c.skipped_effects.delete(h),m&&c.skipped_effects.add(m),c.add_callback(_)}else _();u&&p(!0)};I(()=>{l=!1,T(y),l||d(null,null)},k),o&&(r=P)}export{Z as i};
