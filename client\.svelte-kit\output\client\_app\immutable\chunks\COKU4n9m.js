import{R as L,S as h,T as B,y as E,V as D,t as N,W as U,n as O,X as Y,B as v,Y as T,Z as M,_ as $,a0 as m,D as q,a1 as K,a2 as Z,a3 as z,a4 as C,a5 as G,a6 as V,a7 as W,a8 as X,a9 as j,aa as F,ab as H}from"./UXlYzocm.js";import{s as J,g as Q}from"./D8RLQ4bx.js";function R(e,r){return e===r||e?.[D]===r}function se(e={},r,n,s){return L(()=>{var a,i;return h(()=>{a=i,i=[],E(()=>{e!==n(...i)&&(r(e,...i),a&&R(n(...a),e)&&r(null,...a))})}),()=>{B(()=>{i&&R(n(...i),e)&&r(null,...i)})}}),e}let o=!1,P=Symbol();function ie(e,r,n){const s=n[r]??={store:null,source:Y(void 0),unsubscribe:O};if(s.store!==e&&!(P in n))if(s.unsubscribe(),s.store=e??null,e==null)s.source.v=void 0,s.unsubscribe=O;else{var a=!0;s.unsubscribe=J(e,i=>{a?s.source.v=i:T(s.source,i)}),a=!1}return e&&P in n?Q(e):v(s.source)}function ue(){const e={};function r(){N(()=>{for(var n in e)e[n].unsubscribe();U(e,P,{enumerable:!1,value:!0})})}return[e,r]}function k(e){var r=o;try{return o=!1,[e(),o]}finally{o=r}}const ee={get(e,r){if(!e.exclude.includes(r))return e.props[r]},set(e,r){return!1},getOwnPropertyDescriptor(e,r){if(!e.exclude.includes(r)&&r in e.props)return{enumerable:!0,configurable:!0,value:e.props[r]}},has(e,r){return e.exclude.includes(r)?!1:r in e.props},ownKeys(e){return Reflect.ownKeys(e.props).filter(r=>!e.exclude.includes(r))}};function ae(e,r,n){return new Proxy({props:e,exclude:r},ee)}function te(e,r,n,s){var a=!X||(n&j)!==0,i=(n&W)!==0,x=(n&V)!==0,t=s,b=!0,g=()=>(b&&(b=!1,t=x?E(s):s),t),d;if(i){var A=D in e||H in e;d=M(e,r)?.set??(A&&r in e?u=>e[r]=u:void 0)}var l,p=!1;i?[l,p]=k(()=>e[r]):l=e[r],l===void 0&&s!==void 0&&(l=g(),d&&(a&&$(),d(l)));var f;if(a?f=()=>{var u=e[r];return u===void 0?g():(b=!0,u)}:f=()=>{var u=e[r];return u!==void 0&&(t=void 0),u===void 0?t:u},a&&(n&m)===0)return f;if(d){var w=e.$$legacy;return function(u,_){return arguments.length>0?((!a||!_||w||p)&&d(_?f():u),u):f()}}var S=!1,c=((n&F)!==0?q:K)(()=>(S=!1,f()));i&&v(c);var y=C;return function(u,_){if(arguments.length>0){const I=_?v(c):a&&i?Z(u):u;return T(c,I),S=!0,t!==void 0&&(t=I),u}return z&&S||(y.f&G)!==0?c.v:v(c)}}export{ie as a,se as b,te as p,ae as r,ue as s};
