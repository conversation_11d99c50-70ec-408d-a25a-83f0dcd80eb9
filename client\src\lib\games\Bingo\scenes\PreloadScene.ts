import Phaser from 'phaser';

/**
 * PreloadScene - Loads all assets before game starts
 */
export default class PreloadScene extends Phaser.Scene {
  constructor() {
    super('PreloadScene');
  }

  preload() {
    const { width, height } = this.cameras.main;

    // Create loading bar
    const bgBar = this.add.rectangle(
        width / 2,
        height / 2,
        width / 2,
        20,
        0x232323
    );

    const progressBar = this.add.rectangle(
        bgBar.x - bgBar.width / 2,
        bgBar.y,
        0,
        bgBar.height,
        0x00ff00
    );
    progressBar.setOrigin(0, 0.5);

    const loadingText = this.add.text(
        width / 2,
        height / 2 - 30,
        'Loading...',
        {
          font: '24px Arial',
          color: '#ffffff'
        }
    ).setOrigin(0.5);

    // Update progress bar as assets are loaded
    this.load.on('progress', (value: number) => {
      progressBar.width = bgBar.width * value;
    });

    // Remove progress bar when complete
    this.load.on('complete', () => {
      progressBar.destroy();
      bgBar.destroy();
      loadingText.destroy();
    });

    // Load images
    this.load.image('game_background', '/assets/images/game_bg.png');
    this.load.image('countdown-3', '/assets/images/countdown-3.png');
    this.load.image('countdown-2', '/assets/images/countdown-2.png');
    this.load.image('countdown-1', '/assets/images/countdown-1.png');
    this.load.image('countdown-go', '/assets/images/countdown-go.png');

    // Load SVG assets
    this.load.svg('heart', '/assets/images/mdi--heart.svg');
    this.load.svg('heart_outline', '/assets/images/mdi-light--heart.svg');
    this.load.svg('heart_broken', '/assets/images/mdi--heart-broken.svg');
    
    // GameStartScene assets
    this.load.image('game_name', '/assets-bingo/images/game_name.svg');
    this.load.image('button_bg', '/assets/images/button_bg.svg');
    this.load.image('game_start', '/assets/images/game_start.png');

    // Load Timer Assets
    this.load.image('timer_icon', '/assets/images/timer_icon.png');
    
    // GameEndScene assets
    this.load.image('game_over', '/assets/images/game_over.svg');
    this.load.image('back_to_lobby', '/assets/images/back_to_lobby.png');

    // Load audio - common sounds from centralized location
    this.load.audio('click', ['/assets/audio/click.mp3', '/assets/audio/click.ogg']);
    this.load.audio('wrong', ['/assets/audio/wrong.ogg', '/assets/audio/wrong.mp3', '/assets/audio/wrong.wav']);
    this.load.audio('countdown', ['/assets/audio/countdown.mp3', '/assets/audio/countdown.ogg']);
    this.load.audio('go', ['/assets/audio/go.mp3', '/assets/audio/go.wav']);

    // Load game-specific audio
    this.load.audio('match', ['/assets-bingo/audio/match.mp3', '/assets-bingo/audio/match.ogg']);
    this.load.audio('win', ['/assets-bingo/audio/win.mp3', '/assets-bingo/audio/win.ogg']);
    this.load.audio('number-appear', ['/assets-bingo/audio/number_appear.mp3', '/assets-bingo/audio/number_appear.ogg']);
  }

  create() {
    this.scene.start('GameStartScene');
  }
}