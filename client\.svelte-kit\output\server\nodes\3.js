

export const index = 3;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/game/_id_/_page.svelte.js')).default;
export const universal = {
  "ssr": false
};
export const universal_id = "src/routes/game/[id]/+page.ts";
export const imports = ["_app/immutable/nodes/3.uFkFLWck.js","_app/immutable/chunks/DsnmJJEf.js","_app/immutable/chunks/UXlYzocm.js","_app/immutable/chunks/AD_Ai2mn.js","_app/immutable/chunks/DLbaRTQ9.js","_app/immutable/chunks/COKU4n9m.js","_app/immutable/chunks/D8RLQ4bx.js","_app/immutable/chunks/DdHBXBi3.js","_app/immutable/chunks/ByeBDOvZ.js","_app/immutable/chunks/CcjTyetE.js","_app/immutable/chunks/nPv1uoDA.js"];
export const stylesheets = ["_app/immutable/assets/3.M6-0dyh5.css"];
export const fonts = [];
