var Ie=Array.isArray,Pe=Array.prototype.indexOf,kn=Array.from,Kt=Object.defineProperty,it=Object.getOwnPropertyDescriptor,Me=Object.getOwnPropertyDescriptors,Fe=Object.prototype,Le=Array.prototype,ne=Object.getPrototypeOf,Gt=Object.isExtensible;const On=()=>{};function Nn(t){return t()}function re(t){for(var e=0;e<t.length;e++)t[e]()}function je(){var t,e,n=new Promise((r,a)=>{t=r,e=a});return{promise:n,resolve:t,reject:e}}const x=2,Ct=4,mt=8,ot=16,P=32,nt=64,se=128,k=256,vt=512,y=1024,T=2048,G=4096,q=8192,rt=16384,Dt=32768,ae=65536,Wt=1<<17,qe=1<<18,It=1<<19,Pt=1<<20,Rt=1<<21,Mt=1<<22,Y=1<<23,H=Symbol("$state"),Cn=Symbol("legacy props"),Dn=Symbol(""),Ft=new class extends Error{name="StaleReactionError";message="The reaction that called `getAbortSignal()` was re-run or destroyed"},Lt=3,ie=8;function Ye(){throw new Error("https://svelte.dev/e/await_outside_boundary")}function He(t){throw new Error("https://svelte.dev/e/lifecycle_outside_component")}function Ue(){throw new Error("https://svelte.dev/e/async_derived_orphan")}function Be(t){throw new Error("https://svelte.dev/e/effect_in_teardown")}function Ve(){throw new Error("https://svelte.dev/e/effect_in_unowned_derived")}function $e(t){throw new Error("https://svelte.dev/e/effect_orphan")}function Ke(){throw new Error("https://svelte.dev/e/effect_update_depth_exceeded")}function Pn(){throw new Error("https://svelte.dev/e/get_abort_signal_outside_reaction")}function Mn(){throw new Error("https://svelte.dev/e/hydration_failed")}function Fn(t){throw new Error("https://svelte.dev/e/lifecycle_legacy_only")}function Ln(t){throw new Error("https://svelte.dev/e/props_invalid_value")}function Ge(){throw new Error("https://svelte.dev/e/state_descriptors_fixed")}function We(){throw new Error("https://svelte.dev/e/state_prototype_fixed")}function Xe(){throw new Error("https://svelte.dev/e/state_unsafe_mutation")}const jn=1,qn=2,Yn=4,Hn=8,Un=16,Bn=1,Vn=2,$n=4,Kn=8,Gn=16,Ze=1,ze=2,Je="[",Qe="[!",tn="]",jt={},E=Symbol(),Wn="http://www.w3.org/1999/xhtml",Xn="@attach";function qt(t){console.warn("https://svelte.dev/e/hydration_mismatch")}function Zn(){console.warn("https://svelte.dev/e/select_multiple_invalid_value")}let O=!1;function zn(t){O=t}let p;function J(t){if(t===null)throw qt(),jt;return p=t}function en(){return J(W(p))}function Jn(t){if(O){if(W(p)!==null)throw qt(),jt;p=t}}function Qn(t=1){if(O){for(var e=t,n=p;e--;)n=W(n);p=n}}function tr(){for(var t=0,e=p;;){if(e.nodeType===ie){var n=e.data;if(n===tn){if(t===0)return e;t-=1}else(n===Je||n===Qe)&&(t+=1)}var r=W(e);e.remove(),e=r}}function er(t){if(!t||t.nodeType!==ie)throw qt(),jt;return t.data}function fe(t){return t===this.v}function nn(t,e){return t!=t?e==e:t!==e||t!==null&&typeof t=="object"||typeof t=="function"}function le(t){return!nn(t,this.v)}let Et=!1;function nr(){Et=!0}let w=null;function ht(t){w=t}function rr(t){return gt().get(t)}function sr(t,e){return gt().set(t,e),e}function ar(t){return gt().has(t)}function ir(){return gt()}function fr(t,e=!1,n){w={p:w,c:null,e:null,s:t,x:null,l:Et&&!e?{s:null,u:null,$:[]}:null}}function lr(t){var e=w,n=e.e;if(n!==null){e.e=null;for(var r of n)Ee(r)}return w=e.p,{}}function ct(){return!Et||w!==null&&w.l===null}function gt(t){return w===null&&He(),w.c??=new Map(rn(w)||void 0)}function rn(t){let e=t.p;for(;e!==null;){const n=e.c;if(n!==null)return n;e=e.p}return null}const sn=new WeakMap;function an(t){var e=_;if(e===null)return c.f|=Y,t;if((e.f&Dt)===0){if((e.f&se)===0)throw!e.parent&&t instanceof Error&&ue(t),t;e.b.error(t)}else Yt(t,e)}function Yt(t,e){for(;e!==null;){if((e.f&se)!==0)try{e.b.error(t);return}catch(n){t=n}e=e.parent}throw t instanceof Error&&ue(t),t}function ue(t){const e=sn.get(t);e&&(Kt(t,"message",{value:e.message}),Kt(t,"stack",{value:e.stack}))}let lt=[],St=[];function oe(){var t=lt;lt=[],re(t)}function fn(){var t=St;St=[],re(t)}function ur(t){lt.length===0&&queueMicrotask(oe),lt.push(t)}function ln(){lt.length>0&&oe(),St.length>0&&fn()}function un(){for(var t=_.b;t!==null&&!t.has_pending_snippet();)t=t.parent;return t===null&&Ye(),t}function Ht(t){var e=x|T,n=c!==null&&(c.f&x)!==0?c:null;return _===null||n!==null&&(n.f&k)!==0?e|=k:_.f|=It,{ctx:w,deps:null,effects:null,equals:fe,f:e,fn:t,reactions:null,rv:0,v:E,wv:0,parent:n??_,ac:null}}function on(t,e){let n=_;n===null&&Ue();var r=n.b,a=void 0,s=Bt(E),f=null,u=!c;return mn(()=>{try{var i=t()}catch(h){i=Promise.reject(h)}var l=()=>i;a=f?.then(l,l)??Promise.resolve(i),f=a;var o=R,v=r.pending;u&&(r.update_pending_count(1),v||o.increment());const d=(h,A=void 0)=>{f=null,v||o.activate(),A?A!==Ft&&(s.f|=Y,Ot(s,A)):((s.f&Y)!==0&&(s.f^=Y),Ot(s,h)),u&&(r.update_pending_count(-1),v||o.decrement()),ve()};if(a.then(d,h=>d(null,h||"unknown")),o)return()=>{queueMicrotask(()=>o.neuter())}}),new Promise(i=>{function l(o){function v(){o===a?i(s):l(a)}o.then(v,v)}l(a)})}function or(t){const e=Ht(t);return Re(e),e}function cn(t){const e=Ht(t);return e.equals=le,e}function ce(t){var e=t.effects;if(e!==null){t.effects=null;for(var n=0;n<e.length;n+=1)K(e[n])}}function _n(t){for(var e=t.parent;e!==null;){if((e.f&x)===0)return e;e=e.parent}return null}function Ut(t){var e,n=_;pt(_n(t));try{ce(t),e=Oe(t)}finally{pt(n)}return e}function _e(t){var e=Ut(t);if(t.equals(e)||(t.v=e,t.wv=Se()),!st)if(Q!==null)Q.set(t,t.v);else{var n=(L||(t.f&k)!==0)&&t.deps!==null?G:y;m(t,n)}}function vn(t,e,n){const r=ct()?Ht:cn;if(e.length===0){n(t.map(r));return}var a=R,s=_,f=hn(),u=un();Promise.all(e.map(i=>on(i))).then(i=>{a?.activate(),f();try{n([...t.map(r),...i])}catch(l){(s.f&rt)===0&&Yt(l,s)}a?.deactivate(),ve()}).catch(i=>{u.error(i)})}function hn(){var t=_,e=c,n=w;return function(){pt(t),et(e),ht(n)}}function ve(){pt(null),et(null),ht(null)}const _t=new Set;let R=null,Q=null,Xt=new Set,dt=[];function he(){const t=dt.shift();dt.length>0&&queueMicrotask(he),t()}let X=[],bt=null,kt=!1;class V{#l=new Map;#a=new Map;#i=new Set;#e=0;#u=null;#o=!1;#r=[];#f=[];#n=[];#t=[];#s=[];skipped_effects=new Set;#_(e){X=[];var n=null;if(_t.size>1){n=new Map,Q=new Map;for(const[s,f]of this.#l)n.set(s,{v:s.v,wv:s.wv}),s.v=f;for(const s of _t)if(s!==this)for(const[f,u]of s.#a)n.has(f)||(n.set(f,{v:f.v,wv:f.wv}),f.v=u)}for(const s of e)this.#v(s);if(this.#r.length===0&&this.#e===0){var r=this.#n,a=this.#t;this.#n=[],this.#t=[],this.#s=[],this.#c(),Zt(r),Zt(a),this.#u?.resolve()}else{for(const s of this.#n)m(s,y);for(const s of this.#t)m(s,y);for(const s of this.#s)m(s,y)}if(n){for(const[s,{v:f,wv:u}]of n)s.wv<=u&&(s.v=f);Q=null}for(const s of this.#r)ft(s);for(const s of this.#f)ft(s);this.#r=[],this.#f=[]}#v(e){e.f^=y;for(var n=e.first;n!==null;){var r=n.f,a=(r&(P|nt))!==0,s=a&&(r&y)!==0,f=s||(r&q)!==0||this.skipped_effects.has(n);if(!f&&n.fn!==null){if(a)n.f^=y;else if((r&Ct)!==0)this.#t.push(n);else if(Tt(n))if((r&Mt)!==0){var u=n.b?.pending?this.#f:this.#r;u.push(n)}else(n.f&ot)!==0&&this.#s.push(n),ft(n);var i=n.first;if(i!==null){n=i;continue}}var l=n.parent;for(n=n.next;n===null&&l!==null;)n=l.next,l=l.parent}}capture(e,n){this.#a.has(e)||this.#a.set(e,n),this.#l.set(e,e.v)}activate(){R=this}deactivate(){R=null;for(const e of Xt)if(Xt.delete(e),e(),R!==null)break}neuter(){this.#o=!0}flush(){X.length>0?this.flush_effects():this.#c(),R===this&&(this.#e===0&&_t.delete(this),this.deactivate())}flush_effects(){var e=z;kt=!0;try{var n=0;for(Qt(!0);X.length>0;){if(n++>1e3){var r,a;pn()}this.#_(X),U.clear()}}finally{kt=!1,Qt(e),bt=null}}#c(){if(!this.#o)for(const e of this.#i)e();this.#i.clear()}increment(){this.#e+=1}decrement(){if(this.#e-=1,this.#e===0){for(const e of this.#n)m(e,T),j(e);for(const e of this.#t)m(e,T),j(e);for(const e of this.#s)m(e,T),j(e);this.#n=[],this.#t=[],this.flush()}else this.deactivate()}add_callback(e){this.#i.add(e)}settled(){return(this.#u??=je()).promise}static ensure(e=!0){if(R===null){const n=R=new V;_t.add(R),e&&V.enqueue(()=>{R===n&&n.flush()})}return R}static enqueue(e){dt.length===0&&queueMicrotask(he),dt.unshift(e)}}function dn(t){var e;const n=V.ensure(!1);for(t&&(n.flush_effects(),e=t());;){if(ln(),X.length===0)return n===R&&n.flush(),bt=null,e;n.flush_effects()}}function pn(){try{Ke()}catch(t){Yt(t,bt)}}function Zt(t){var e=t.length;if(e!==0){for(var n=0;n<e;n++){var r=t[n];if((r.f&(rt|q))===0&&Tt(r)){var a=wt;if(ft(r),r.deps===null&&r.first===null&&r.nodes_start===null&&(r.teardown===null&&r.ac===null?Te(r):r.fn=null),wt>a&&(r.f&Pt)!==0)break}}for(;n<e;n+=1)j(t[n])}}function j(t){for(var e=bt=t;e.parent!==null;){e=e.parent;var n=e.f;if(kt&&e===_&&(n&ot)!==0)return;if((n&(nt|P))!==0){if((n&y)===0)return;e.f^=y}}X.push(e)}const U=new Map;function Bt(t,e){var n={f:0,v:t,reactions:null,equals:fe,rv:0,wv:0};return n}function M(t,e){const n=Bt(t);return Re(n),n}function cr(t,e=!1,n=!0){const r=Bt(t);return e||(r.equals=le),Et&&n&&w!==null&&w.l!==null&&(w.l.s??=[]).push(r),r}function F(t,e,n=!1){c!==null&&(!N||(c.f&Wt)!==0)&&ct()&&(c.f&(x|ot|Mt|Wt))!==0&&!D?.includes(t)&&Xe();let r=n?at(e):e;return Ot(t,r)}function Ot(t,e){if(!t.equals(e)){var n=t.v;st?U.set(t,e):U.set(t,n),t.v=e,V.ensure().capture(t,n),(t.f&x)!==0&&((t.f&T)!==0&&Ut(t),m(t,(t.f&k)===0?y:G)),t.wv=Se(),de(t,T),ct()&&_!==null&&(_.f&y)!==0&&(_.f&(P|nt))===0&&(S===null?xn([t]):S.push(t))}return e}function _r(t,e=1){var n=Z(t),r=e===1?n++:n--;return F(t,n),r}function At(t){F(t,t.v+1)}function de(t,e){var n=t.reactions;if(n!==null)for(var r=ct(),a=n.length,s=0;s<a;s++){var f=n[s],u=f.f;!r&&f===_||((u&T)===0&&m(f,e),(u&x)!==0?de(f,G):(u&T)===0&&j(f))}}function at(t){if(typeof t!="object"||t===null||H in t)return t;const e=ne(t);if(e!==Fe&&e!==Le)return t;var n=new Map,r=Ie(t),a=M(0),s=B,f=u=>{if(B===s)return u();var i=c,l=B;et(null),ee(s);var o=u();return et(i),ee(l),o};return r&&n.set("length",M(t.length)),new Proxy(t,{defineProperty(u,i,l){(!("value"in l)||l.configurable===!1||l.enumerable===!1||l.writable===!1)&&Ge();var o=n.get(i);return o===void 0?o=f(()=>{var v=M(l.value);return n.set(i,v),v}):F(o,l.value,!0),!0},deleteProperty(u,i){var l=n.get(i);if(l===void 0){if(i in u){const o=f(()=>M(E));n.set(i,o),At(a)}}else F(l,E),At(a);return!0},get(u,i,l){if(i===H)return t;var o=n.get(i),v=i in u;if(o===void 0&&(!v||it(u,i)?.writable)&&(o=f(()=>{var h=at(v?u[i]:E),A=M(h);return A}),n.set(i,o)),o!==void 0){var d=Z(o);return d===E?void 0:d}return Reflect.get(u,i,l)},getOwnPropertyDescriptor(u,i){var l=Reflect.getOwnPropertyDescriptor(u,i);if(l&&"value"in l){var o=n.get(i);o&&(l.value=Z(o))}else if(l===void 0){var v=n.get(i),d=v?.v;if(v!==void 0&&d!==E)return{enumerable:!0,configurable:!0,value:d,writable:!0}}return l},has(u,i){if(i===H)return!0;var l=n.get(i),o=l!==void 0&&l.v!==E||Reflect.has(u,i);if(l!==void 0||_!==null&&(!o||it(u,i)?.writable)){l===void 0&&(l=f(()=>{var d=o?at(u[i]):E,h=M(d);return h}),n.set(i,l));var v=Z(l);if(v===E)return!1}return o},set(u,i,l,o){var v=n.get(i),d=i in u;if(r&&i==="length")for(var h=l;h<v.v;h+=1){var A=n.get(h+"");A!==void 0?F(A,E):h in u&&(A=f(()=>M(E)),n.set(h+"",A))}if(v===void 0)(!d||it(u,i)?.writable)&&(v=f(()=>M(void 0)),F(v,at(l)),n.set(i,v));else{d=v.v!==E;var De=f(()=>at(l));F(v,De)}var Vt=Reflect.getOwnPropertyDescriptor(u,i);if(Vt?.set&&Vt.set.call(o,l),!d){if(r&&typeof i=="string"){var $t=n.get("length"),xt=Number(i);Number.isInteger(xt)&&xt>=$t.v&&F($t,xt+1)}At(a)}return!0},ownKeys(u){Z(a);var i=Reflect.ownKeys(u).filter(v=>{var d=n.get(v);return d===void 0||d.v!==E});for(var[l,o]of n)o.v!==E&&!(l in u)&&i.push(l);return i},setPrototypeOf(){We()}})}function zt(t){try{if(t!==null&&typeof t=="object"&&H in t)return t[H]}catch{}return t}function vr(t,e){return Object.is(zt(t),zt(e))}var Jt,wn,pe,we,ye;function hr(){if(Jt===void 0){Jt=window,wn=document,pe=/Firefox/.test(navigator.userAgent);var t=Element.prototype,e=Node.prototype,n=Text.prototype;we=it(e,"firstChild").get,ye=it(e,"nextSibling").get,Gt(t)&&(t.__click=void 0,t.__className=void 0,t.__attributes=null,t.__style=void 0,t.__e=void 0),Gt(n)&&(n.__t=void 0)}}function tt(t=""){return document.createTextNode(t)}function $(t){return we.call(t)}function W(t){return ye.call(t)}function dr(t,e){if(!O)return $(t);var n=$(p);if(n===null)n=p.appendChild(tt());else if(e&&n.nodeType!==Lt){var r=tt();return n?.before(r),J(r),r}return J(n),n}function pr(t,e){if(!O){var n=$(t);return n instanceof Comment&&n.data===""?W(n):n}return p}function wr(t,e=1,n=!1){let r=O?p:t;for(var a;e--;)a=r,r=W(r);if(!O)return r;if(n&&r?.nodeType!==Lt){var s=tt();return r===null?a?.after(s):r.before(s),J(s),s}return J(r),r}function yr(t){t.textContent=""}function mr(){return!1}function me(t){_===null&&c===null&&$e(),c!==null&&(c.f&k)!==0&&_===null&&Ve(),st&&Be()}function yn(t,e){var n=e.last;n===null?e.last=e.first=t:(n.next=t,t.prev=n,e.last=t)}function C(t,e,n,r=!0){var a=_;a!==null&&(a.f&q)!==0&&(t|=q);var s={ctx:w,deps:null,nodes_start:null,nodes_end:null,f:t|T,first:null,fn:e,last:null,next:null,parent:a,b:a&&a.b,prev:null,teardown:null,transitions:null,wv:0,ac:null};if(n)try{ft(s),s.f|=Dt}catch(i){throw K(s),i}else e!==null&&j(s);var f=n&&s.deps===null&&s.first===null&&s.nodes_start===null&&s.teardown===null&&(s.f&It)===0;if(!f&&r&&(a!==null&&yn(s,a),c!==null&&(c.f&x)!==0)){var u=c;(u.effects??=[]).push(s)}return s}function Er(t){const e=C(mt,null,!1);return m(e,y),e.teardown=t,e}function gr(t){me();var e=_.f,n=!c&&(e&P)!==0&&(e&Dt)===0;if(n){var r=w;(r.e??=[]).push(t)}else return Ee(t)}function Ee(t){return C(Ct|Pt,t,!1)}function br(t){return me(),C(mt|Pt,t,!0)}function Tr(t){V.ensure();const e=C(nt,t,!0);return(n={})=>new Promise(r=>{n.outro?bn(e,()=>{K(e),r(void 0)}):(K(e),r(void 0))})}function xr(t){return C(Ct,t,!1)}function mn(t){return C(Mt|It,t,!0)}function Ar(t,e=0){return C(mt|e,t,!0)}function Rr(t,e=[],n=[]){vn(e,n,r=>{C(mt,()=>t(...r.map(Z)),!0)})}function Sr(t,e=0){var n=C(ot|e,t,!0);return n}function kr(t,e=!0){return C(P,t,!0,e)}function ge(t){var e=t.teardown;if(e!==null){const n=st,r=c;te(!0),et(null);try{e.call(null)}finally{te(n),et(r)}}}function be(t,e=!1){var n=t.first;for(t.first=t.last=null;n!==null;){n.ac?.abort(Ft);var r=n.next;(n.f&nt)!==0?n.parent=null:K(n,e),n=r}}function En(t){for(var e=t.first;e!==null;){var n=e.next;(e.f&P)===0&&K(e),e=n}}function K(t,e=!0){var n=!1;(e||(t.f&qe)!==0)&&t.nodes_start!==null&&t.nodes_end!==null&&(gn(t.nodes_start,t.nodes_end),n=!0),be(t,e&&!n),yt(t,0),m(t,rt);var r=t.transitions;if(r!==null)for(const s of r)s.stop();ge(t);var a=t.parent;a!==null&&a.first!==null&&Te(t),t.next=t.prev=t.teardown=t.ctx=t.deps=t.fn=t.nodes_start=t.nodes_end=t.ac=null}function gn(t,e){for(;t!==null;){var n=t===e?null:W(t);t.remove(),t=n}}function Te(t){var e=t.parent,n=t.prev,r=t.next;n!==null&&(n.next=r),r!==null&&(r.prev=n),e!==null&&(e.first===t&&(e.first=r),e.last===t&&(e.last=n))}function bn(t,e){var n=[];xe(t,n,!0),Tn(n,()=>{K(t),e&&e()})}function Tn(t,e){var n=t.length;if(n>0){var r=()=>--n||e();for(var a of t)a.out(r)}else e()}function xe(t,e,n){if((t.f&q)===0){if(t.f^=q,t.transitions!==null)for(const f of t.transitions)(f.is_global||n)&&e.push(f);for(var r=t.first;r!==null;){var a=r.next,s=(r.f&ae)!==0||(r.f&P)!==0;xe(r,e,s?n:!1),r=a}}}function Or(t){Ae(t,!0)}function Ae(t,e){if((t.f&q)!==0){t.f^=q,(t.f&y)===0&&(m(t,T),j(t));for(var n=t.first;n!==null;){var r=n.next,a=(n.f&ae)!==0||(n.f&P)!==0;Ae(n,a?e:!1),n=r}if(t.transitions!==null)for(const s of t.transitions)(s.is_global||e)&&s.in()}}let z=!1;function Qt(t){z=t}let st=!1;function te(t){st=t}let c=null,N=!1;function et(t){c=t}let _=null;function pt(t){_=t}let D=null;function Re(t){c!==null&&(D===null?D=[t]:D.push(t))}let g=null,b=0,S=null;function xn(t){S=t}let wt=1,ut=0,B=ut;function ee(t){B=t}let L=!1;function Se(){return++wt}function Tt(t){var e=t.f;if((e&T)!==0)return!0;if((e&G)!==0){var n=t.deps,r=(e&k)!==0;if(n!==null){var a,s,f=(e&vt)!==0,u=r&&_!==null&&!L,i=n.length;if((f||u)&&(_===null||(_.f&rt)===0)){var l=t,o=l.parent;for(a=0;a<i;a++)s=n[a],(f||!s?.reactions?.includes(l))&&(s.reactions??=[]).push(l);f&&(l.f^=vt),u&&o!==null&&(o.f&k)===0&&(l.f^=k)}for(a=0;a<i;a++)if(s=n[a],Tt(s)&&_e(s),s.wv>t.wv)return!0}(!r||_!==null&&!L)&&m(t,y)}return!1}function ke(t,e,n=!0){var r=t.reactions;if(r!==null&&!D?.includes(t))for(var a=0;a<r.length;a++){var s=r[a];(s.f&x)!==0?ke(s,e,!1):e===s&&(n?m(s,T):(s.f&y)!==0&&m(s,G),j(s))}}function Oe(t){var e=g,n=b,r=S,a=c,s=L,f=D,u=w,i=N,l=B,o=t.f;g=null,b=0,S=null,L=(o&k)!==0&&(N||!z||c===null),c=(o&(P|nt))===0?t:null,D=null,ht(t.ctx),N=!1,B=++ut,t.ac!==null&&(t.ac.abort(Ft),t.ac=null);try{t.f|=Rt;var v=(0,t.fn)(),d=t.deps;if(g!==null){var h;if(yt(t,b),d!==null&&b>0)for(d.length=b+g.length,h=0;h<g.length;h++)d[b+h]=g[h];else t.deps=d=g;if(!L||(o&x)!==0&&t.reactions!==null)for(h=b;h<d.length;h++)(d[h].reactions??=[]).push(t)}else d!==null&&b<d.length&&(yt(t,b),d.length=b);if(ct()&&S!==null&&!N&&d!==null&&(t.f&(x|G|T))===0)for(h=0;h<S.length;h++)ke(S[h],t);return a!==null&&a!==t&&(ut++,S!==null&&(r===null?r=S:r.push(...S))),(t.f&Y)!==0&&(t.f^=Y),v}catch(A){return an(A)}finally{t.f^=Rt,g=e,b=n,S=r,c=a,L=s,D=f,ht(u),N=i,B=l}}function An(t,e){let n=e.reactions;if(n!==null){var r=Pe.call(n,t);if(r!==-1){var a=n.length-1;a===0?n=e.reactions=null:(n[r]=n[a],n.pop())}}n===null&&(e.f&x)!==0&&(g===null||!g.includes(e))&&(m(e,G),(e.f&(k|vt))===0&&(e.f^=vt),ce(e),yt(e,0))}function yt(t,e){var n=t.deps;if(n!==null)for(var r=e;r<n.length;r++)An(t,n[r])}function ft(t){var e=t.f;if((e&rt)===0){m(t,y);var n=_,r=z;_=t,z=!0;try{(e&ot)!==0?En(t):be(t),ge(t);var a=Oe(t);t.teardown=typeof a=="function"?a:null,t.wv=wt;var s}finally{z=r,_=n}}}async function Nr(){await Promise.resolve(),dn()}function Cr(){return V.ensure().settled()}function Z(t){var e=t.f,n=(e&x)!==0;if(c!==null&&!N){var r=_!==null&&(_.f&rt)!==0;if(!r&&!D?.includes(t)){var a=c.deps;if((c.f&Rt)!==0)t.rv<ut&&(t.rv=ut,g===null&&a!==null&&a[b]===t?b++:g===null?g=[t]:(!L||!g.includes(t))&&g.push(t));else{(c.deps??=[]).push(t);var s=t.reactions;s===null?t.reactions=[c]:s.includes(c)||s.push(c)}}}else if(n&&t.deps===null&&t.effects===null){var f=t,u=f.parent;u!==null&&(u.f&k)===0&&(f.f^=k)}if(st){if(U.has(t))return U.get(t);if(n){f=t;var i=f.v;return((f.f&y)===0&&f.reactions!==null||Ne(f))&&(i=Ut(f)),U.set(f,i),i}}else if(n){if(f=t,Q?.has(f))return Q.get(f);Tt(f)&&_e(f)}if((t.f&Y)!==0)throw t.v;return t.v}function Ne(t){if(t.v===E)return!0;if(t.deps===null)return!1;for(const e of t.deps)if(U.has(e)||(e.f&x)!==0&&Ne(e))return!0;return!1}function Dr(t){var e=N;try{return N=!0,t()}finally{N=e}}const Rn=-7169;function m(t,e){t.f=t.f&Rn|e}function Ir(t){if(!(typeof t!="object"||!t||t instanceof EventTarget)){if(H in t)Nt(t);else if(!Array.isArray(t))for(let e in t){const n=t[e];typeof n=="object"&&n&&H in n&&Nt(n)}}}function Nt(t,e=new Set){if(typeof t=="object"&&t!==null&&!(t instanceof EventTarget)&&!e.has(t)){e.add(t),t instanceof Date&&t.getTime();for(let r in t)try{Nt(t[r],e)}catch{}const n=ne(t);if(n!==Object.prototype&&n!==Array.prototype&&n!==Map.prototype&&n!==Set.prototype&&n!==Date.prototype){const r=Me(n);for(let a in r){const s=r[a].get;if(s)try{s.call(t)}catch{}}}}}function Ce(t){var e=document.createElement("template");return e.innerHTML=t.replaceAll("<!>","<!---->"),e.content}function I(t,e){var n=_;n.nodes_start===null&&(n.nodes_start=t,n.nodes_end=e)}function Pr(t,e){var n=(e&Ze)!==0,r=(e&ze)!==0,a,s=!t.startsWith("<!>");return()=>{if(O)return I(p,null),p;a===void 0&&(a=Ce(s?t:"<!>"+t),n||(a=$(a)));var f=r||pe?document.importNode(a,!0):a.cloneNode(!0);if(n){var u=$(f),i=f.lastChild;I(u,i)}else I(f,f);return f}}function Sn(t,e,n="svg"){var r=!t.startsWith("<!>"),a=`<${n}>${r?t:"<!>"+t}</${n}>`,s;return()=>{if(O)return I(p,null),p;if(!s){var f=Ce(a),u=$(f);s=$(u)}var i=s.cloneNode(!0);return I(i,i),i}}function Mr(t,e){return Sn(t,e,"svg")}function Fr(t=""){if(!O){var e=tt(t+"");return I(e,e),e}var n=p;return n.nodeType!==Lt&&(n.before(n=tt()),J(n)),I(n,n),n}function Lr(){if(O)return I(p,null),p;var t=document.createDocumentFragment(),e=document.createComment(""),n=tt();return t.append(e,n),I(e,n),t}function jr(t,e){if(O){_.nodes_end=p,en();return}t!==null&&t.before(e)}export{wn as $,Nn as A,Z as B,Ir as C,Ht as D,ae as E,fr as F,Pr as G,Qe as H,pr as I,Rr as J,jr as K,lr as L,dr as M,Jn as N,wr as O,Qn as P,Lr as Q,xr as R,Ar as S,ur as T,E as U,H as V,Kt as W,cr as X,F as Y,it as Z,Ln as _,en as a,$n as a0,cn as a1,at as a2,st as a3,_ as a4,rt as a5,Gn as a6,Kn as a7,Et as a8,Vn as a9,yr as aA,kn as aB,Tr as aC,M as aD,or as aE,Fr as aF,Ot as aG,Bt as aH,qn as aI,jn as aJ,Un as aK,q as aL,xe as aM,Tn as aN,Yn as aO,Hn as aP,Zn as aQ,vr as aR,Wn as aS,ne as aT,Dn as aU,Me as aV,vn as aW,Xn as aX,gn as aY,_r as aZ,Mr as a_,Bn as aa,Cn as ab,nn as ac,He as ad,Pn as ae,c as af,Ie as ag,Fn as ah,dn as ai,ir as aj,rr as ak,ar as al,sr as am,Cr as an,Nr as ao,et as ap,pt as aq,qe as ar,ie as as,Je as at,W as au,hr as av,jt as aw,tn as ax,qt as ay,Mn as az,Sr as b,Ce as c,I as d,nr as e,kr as f,K as g,O as h,p as i,$ as j,tr as k,zn as l,tt as m,On as n,R as o,mr as p,Or as q,er as r,J as s,Er as t,bn as u,w as v,br as w,gr as x,Dr as y,re as z};
