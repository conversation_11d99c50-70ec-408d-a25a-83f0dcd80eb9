import Phaser from 'phaser';
import type GameScene from '../scenes/GameScene';
import type { BingoColumn } from '../types/BingoTypes';

/**
 * BingoCell - Individual cell on the bingo board
 * Equivalent to TableButtonController in Unity
 */
export default class BingoCell extends Phaser.GameObjects.Container {
  public letter: BingoColumn;
  public number: number;
  public marked: boolean;
  public isFree: boolean;
  public blinkTween: Phaser.Tweens.Tween | null;
  public background!: Phaser.GameObjects.Graphics;
  public markedBg!: Phaser.GameObjects.Graphics;
  public numberText!: Phaser.GameObjects.Text;
  public letterText!: Phaser.GameObjects.Text;
  public isBingoHeader?: boolean;

  constructor(scene: GameScene, x: number, y: number, letter: BingoColumn, number: number, isFree: boolean = false) {
    super(scene, x, y);

    // Store properties
    this.scene = scene as GameScene;
    this.letter = letter;
    this.number = number;
    this.isFree = isFree;
    this.name = isFree ? 'FREE' : `${letter}${number}`;
    this.marked = isFree; // FREE space is automatically marked
    this.blinkTween = null;

    // Create visual components
    this.createVisuals();

    // Add interactivity (FREE space should not be interactive)
    if (!isFree) {
      this.setupInteractivity();
    }

    // Add to scene
    scene.add.existing(this);
  }

  createVisuals() {
    // Create cell shape with gradient
    const cellSize = 80;

    // Create graphics for the background with rounded rectangle
    const bgGraphics = this.scene.add.graphics();

    // For inactive cells - use dark background
    bgGraphics.fillStyle(0x111122, 1); // Dark background

    // Keep the teal border
    bgGraphics.lineStyle(3, 0x00E5AE, 1);

    // Draw the background and border
    bgGraphics.fillRoundedRect(-cellSize/2, -cellSize/2, cellSize, cellSize, 16);
    bgGraphics.strokeRoundedRect(-cellSize/2, -cellSize/2, cellSize, cellSize, 16);

    this.add(bgGraphics);
    this.background = bgGraphics;

    // Create marked state with gradient
    const markedGraphics = this.scene.add.graphics();

    // Use the blue gradient for the marked state
    markedGraphics.fillGradientStyle(
        0x3066FF, // Top-left: blue
        0x4752FF, // Top-right: blue-purple
        0x5E4DFF, // Bottom-right: more purple
        0x215EFF, // Bottom-left: blue
        1
    );
    markedGraphics.fillRoundedRect(-cellSize/2, -cellSize/2, cellSize, cellSize, 16);
    markedGraphics.alpha = this.isFree ? 1 : 0; // FREE space starts marked
    this.add(markedGraphics);
    this.markedBg = markedGraphics;

    // Create number text (or FREE text for center space)
    const displayText = this.isFree ? 'FREE' : this.number.toString();
    const fontSize = this.isFree ? '24px' : '36px';

    this.numberText = this.scene.add.text(
        0,
        0,
        displayText,
        {
          fontFamily: '"TT Neoris", Arial, sans-serif',
          fontSize: fontSize,
          color: '#FFFFFF',
          align: 'center',
          fontStyle: 'bold',
          stroke: '#000000',
          strokeThickness: 2,
          shadow: { offsetX: 1, offsetY: 1, color: '#000000', blur: 2, fill: true }
        }
    ).setOrigin(0.5);
    this.add(this.numberText);

    // Letter label
    this.letterText = this.scene.add.text(
        -28,
        -28,
        this.letter,
        {
          fontFamily: '"TT Neoris", Arial, sans-serif',
          fontSize: '16px',
          color: '#FFFFFF',
          align: 'center',
          fontStyle: 'bold'
        }
    ).setOrigin(0.5);
    this.add(this.letterText);

    // Initially hide the letter for regular cells
    if (!this.isBingoHeader) {
      this.letterText.alpha = 0;
    }
  }

  setupInteractivity() {
    // Create an invisible hit area for proper interaction
    const cellSize = 80;
    const hitArea = new Phaser.Geom.Rectangle(-cellSize/2, -cellSize/2, cellSize, cellSize);
    
    // Make the container itself interactive
    this.setInteractive(hitArea, Phaser.Geom.Rectangle.Contains);
    
    // Click handler on the container
    this.on('pointerdown', () => {
      const gameScene = this.scene as GameScene;
      if (!this.marked && !gameScene.gameEnd) {
        gameScene.checkForMatch(this);
      }
    });
    
    // Hover effects
    this.on('pointerover', () => {
      const gameScene = this.scene as GameScene;
      if (!this.marked && !gameScene.gameEnd) {
        // For graphics, use alpha change
        this.background.alpha = 0.8;
        this.scene.game.canvas.style.cursor = 'pointer';
      }
    });
    
    this.on('pointerout', () => {
      const gameScene = this.scene as GameScene;
      if (!this.marked && !gameScene.gameEnd) {
        // Reset alpha
        this.background.alpha = 1;
        this.scene.game.canvas.style.cursor = 'default';
      }
    });
  }

  /**
   * Blink the number to indicate a match
   * Equivalent to blink() in Unity's TableButtonController
   */
  blink() {
    // Stop any previous blinking
    if (this.blinkTween) {
      this.blinkTween.stop();
    }
    
    // Create blinking effect by fading in/out the marked background
    this.blinkTween = this.scene.tweens.add({
      targets: this.markedBg,
      alpha: { from: 0, to: 0.5 },
      duration: 240,
      yoyo: true,
      repeat: 3,
      onComplete: () => {
        this.markedBg.alpha = 0;
        this.blinkTween = null;
      }
    });
  }

  /**
   * Mark the cell as clicked
   * Equivalent to PlayPickUpStar() in Unity's TableButtonController
   */
  mark() {
    if (this.marked) return;
    
    // Set marked state
    this.marked = true;
    
    // Show the marked background with animation
    this.scene.tweens.add({
      targets: this.markedBg,
      alpha: 1,
      duration: 200,
      ease: 'Power2'
    });
    
    // Add a subtle pulse animation to the cell when marked
    this.scene.tweens.add({
      targets: this,
      scale: { from: 1.1, to: 1 },
      duration: 300,
      ease: 'Back.Out'
    });
    
    // Stop any blinking
    if (this.blinkTween) {
      this.blinkTween.stop();
      this.blinkTween = null;
    }
    
    // Create particle effect
    this.createMarkParticles();
    
    // Disable interaction by removing the interactive property
    this.disableInteractive();
  }

  /**
   * Create particle effect for marking
   * Equivalent to particle effect in Unity
   */
  createMarkParticles() {
    // Create a white glow/sparkle effect - more intense to match Unity
    const particles = this.scene.add.particles(this.x, this.y, 'particle-star', {
      speed: { min: 70, max: 180 },
      scale: { start: 0.8, end: 0 },
      lifespan: 800,
      quantity: 20,
      tint: 0x00E5AE, // Aqua color like the cell borders
      rotate: { min: 0, max: 360 },
      emitting: false,
      blendMode: 'ADD'
    });
    
    particles.explode();
    
    // Add a circle flash effect - larger and brighter to match Unity
    const flash = this.scene.add.circle(0, 0, 45, 0x00E5AE, 0.8);
    this.add(flash);
    
    // Animate the flash
    this.scene.tweens.add({
      targets: flash,
      alpha: 0,
      scale: 1.8,
      duration: 500,
      ease: 'Power2',
      onComplete: () => {
        flash.destroy();
      }
    });
    
    // Destroy particles after animation
    this.scene.time.delayedCall(1000, () => {
      particles.destroy();
    });
  }

  /**
   * Create celebratory particle effect for win
   * Equivalent to PlayPickUpStar() when game ends
   */
  createWinParticles() {
    const particles = this.scene.add.particles(this.x, this.y, 'particle-glow', {
      speed: { min: 30, max: 80 },
      scale: { start: 0.5, end: 0 },
      lifespan: 1500,
      quantity: 5,
      frequency: 200,
      tint: 0x00E5AE, // Match the aqua color scheme
      blendMode: 'ADD',
      rotate: { min: 0, max: 360 },
      emitting: true
    });
    
    // Destroy particles after animation
    this.scene.time.delayedCall(3000, () => {
      particles.destroy();
    });
  }
}
