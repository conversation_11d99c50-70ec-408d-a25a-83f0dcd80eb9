export const slugType = Object.freeze({
    history: "tx_id",
    user_id: "user_id",
    game_id: "game_id",
    swap: "swap_id",
    withdrawal: "withdraw_id",
    deposit: "deposit_id",
});
export function numberToSlug(id, type) {
    const encoded = `${id}:${type}`;
    const buffer = Buffer.from(encoded);
    const base64Slug = buffer
        .toString("base64")
        .replace(/\+/g, "-")
        .replace(/\//g, "_")
        .replace(/=+$/, "");
    // return base64Slug;
    return base64Slug;
}
export function slugToNumber(slug, type) {
    const base64 = slug.replace(/-/g, "+").replace(/_/g, "/");
    const buffer = Buffer.from(base64, "base64");
    const decoded = buffer.toString("utf8");
    const [id, decodedType] = decoded.split(":");
    console.log(id, decodedType);
    if (decodedType !== type) {
        console.error(`Slug: Expected type ${type}, got ${decodedType}`);
        return -1;
        // throw new Error(`Slug: Expected type ${type}, got ${decodedType}`);
    }
    return parseInt(id, 10);
}
// console.log(slugToNumber("Mjp1bmRlZmluZWQ", slugType.swap));
