import MatchingCard from '../objects/MatchingCard';
import ScoreManager from '../managers/ScoreManager';
import TimerManager from '../managers/TimerManager';
import LivesManager from '../managers/LivesManager';
import TimerBarUI from '../ui/TimerBarUI';
import Radial<PERSON><PERSON>r<PERSON> from '../ui/RadialTimerUI';
import { GAME_CONFIG, ANIMAL_NAMES, COLOR_NAMES, CATEGORY_TINTS } from '../config/GameConfig';
import SeededRandom from '../utils/KeygenUtil';

export default class GameScene extends Phaser.Scene {
  // 2D array to store animal images by [animal][color]
  private animalImages: string[][] = [];

  // Game elements
  private mainImage!: Phaser.GameObjects.Image;
  private optionCards: MatchingCard[] = [];
  private correctCardIndex: number = 0;

  // UI elements
  // private scoreText!: any; // Custom object with setText method
  // private timeText!: Phaser.GameObjects.Text | Phaser.GameObjects.BitmapText;
  private radialTimerUI!: RadialTimerUI;
  private bonusScoreText!: Phaser.GameObjects.Text;
  
  private UIContainer!: Phaser.GameObjects.Container;

  private scoreManager!: ScoreManager;
  private timerManager!: TimerManager;
  private livesManager!: LivesManager;
  private timerBarUI!: TimerBarUI;

  // UI panels
  private countdownPanel!: Phaser.GameObjects.Container;
  private gamePanel!: Phaser.GameObjects.Container;

  // Game state
  // private score: number = 0;
  private isLocked: boolean = false;
  private isGameOver: boolean = false;
  // private gameTime: number = GAME_CONFIG.GAME_TIME;
  private roundTime: number = GAME_CONFIG.ROUND_TIME;
  private currentRoundTime: number = 0;
  // private gameTimer!: Phaser.Time.TimerEvent;
  private roundTimer!: Phaser.Time.TimerEvent;

  // Seeded random number generator
  private rng: SeededRandom;
  private gameSeed: number;

  constructor(seed?: number) {
    super('GameScene');

    this.rng = new SeededRandom(Date.now());

    // Initialize seeded random number generator
    this.gameSeed = seed ?? Date.now();
    this.rng = new SeededRandom(this.gameSeed);

    console.log(`Bingo Game initialized with seed: ${this.gameSeed}`);
  }

  init(){
    // Initialize managers
    this.scoreManager = new ScoreManager(this, {
      initialScore: 0,
      fontSize: '80px',
      scoreColor: '#33DDFF'
    });

    this.timerManager = new TimerManager(this, {
      duration: GAME_CONFIG.GAME_TIME,
      warningThreshold: 5
    });

    this.livesManager = new LivesManager(this, {
      initialLives: 3
    });

    this.timerBarUI = new TimerBarUI(this, {
      width: this.cameras.main.width * 0.8,
      height: 35,
      x: this.cameras.main.width / 2,
      y: this.cameras.main.height * 0.07
    });

    // Set up timer events
    this.timerManager.on('timeUp', () => this.endGame());
    this.timerManager.on('timerUpdate', (state) => {
      this.timerBarUI.updateProgress(state.progress);
    });

    this.livesManager.on('heartDeducted', (lives) => {
      if (lives === 0) {
        this.endGame();
      }
    });
  }


  create(): void {
    // Get the animal images from the registry
    this.animalImages = this.game.registry.get('animalImages') || [];

    // Enable lights for WebGL renderer
    if (this.sys.game.renderer.type === Phaser.WEBGL) {
      this.lights.enable();
      this.lights.setAmbientColor(0x000000); // Set to black for maximum contrast with glowing elements
    }

    // Set a solid background color that matches the game_bg.png
    this.cameras.main.setBackgroundColor(GAME_CONFIG.BACKGROUND_COLOR);

    // Create a separate background container with gradient that matches the game background
    const backgroundContainer = this.add.container(0, 0);
    backgroundContainer.setDepth(-10); // Extremely low depth to ensure it's behind everything

    // Create a gradient background that matches the game_bg.png
    // First, create a canvas texture for the gradient
    const width = this.cameras.main.width;
    const height = this.cameras.main.height;
    const gradientTexture = this.textures.createCanvas('gradientBg', width, height);

    if (gradientTexture) {
      const context = gradientTexture.getContext();

      // Create a radial gradient from center to edges (dark blue to darker blue)
      const gradient = context.createRadialGradient(
        width/2, height/2, 0,           // inner circle center and radius
        width/2, height/2, height * 0.8  // outer circle center and radius
      );

      // Add color stops that match the game_bg.png
      gradient.addColorStop(0, '#151B30');  // Dark blue at center
      gradient.addColorStop(1, '#0E0F1E');  // Darker blue at edges

      // Fill the canvas with the gradient
      context.fillStyle = gradient;
      context.fillRect(0, 0, width, height);

      // Add some subtle noise/texture
      for (let i = 0; i < 5000; i++) {
        const x = this.rng.next() * width;
        const y = this.rng.next() * height;
        const size = this.rng.next() * 2;
        const alpha = this.rng.next() * 0.05; // Very subtle

        context.fillStyle = `rgba(255, 255, 255, ${alpha})`;
        context.fillRect(x, y, size, size);
      }

      // Refresh the texture
      gradientTexture.refresh();

      // Create an image using this texture
      const gradientBg = this.add.image(0, 0, 'gradientBg').setOrigin(0, 0);
      backgroundContainer.add(gradientBg);

      console.log('Created gradient background as fallback');
    } else {
      // If canvas creation fails, fall back to a simple rectangle
      const baseRect = this.add.rectangle(
        0, 0,
        this.cameras.main.width,
        this.cameras.main.height,
        0x0E0F1E // Dark blue color
      ).setOrigin(0, 0);
      backgroundContainer.add(baseRect);
    }

    // Create the background image with explicit dimensions
    const bgImage = this.add.image(0, 0, 'game_background').setOrigin(0, 0);

    // Scale the image to fit the screen
    bgImage.displayWidth = this.cameras.main.width;
    bgImage.displayHeight = this.cameras.main.height;
    backgroundContainer.add(bgImage);

    // Create the main game panel
    this.gamePanel = this.add.container(0, 0);
    this.gamePanel.setVisible(false);
    this.gamePanel.setDepth(1);

    // Create the countdown panel
    this.createCountdownPanel();

    // Create UI elements in the game panel
    this.createUI();

    // Start the countdown
    this.startCountdown();
  }

  shutdown() {
    // Clean up managers
    if (this.scoreManager) {
      this.scoreManager.destroy();
    }

    if (this.timerManager) {
      this.timerManager.destroy();
    }

    if (this.timerBarUI) {
      this.timerBarUI.destroy();
    }

    // Clean up radial timer UI
    if (this.radialTimerUI) {
      this.radialTimerUI.destroy();
    }

    if (this.livesManager) {
      this.livesManager.destroy();
    }
  }

  /**
   * Create the countdown panel with countdown image
   */
  private createCountdownPanel(): void {
    // Create countdown panel container
    this.countdownPanel = this.add.container(0, 0);
    this.countdownPanel.setDepth(2); // Ensure countdown panel is above both background and game panel

    // Add semi-transparent overlay
    const overlay = this.add.rectangle(
      0, 0, 
      this.cameras.main.width, 
      this.cameras.main.height, 
      0x000000, 0.7
    ).setOrigin(0, 0);

    this.countdownPanel.add(overlay);

    // Create countdown image (scaled to half size)
    const countdownImage = this.add.image(
      this.cameras.main.width / 2,
      this.cameras.main.height / 2,
      'countdown-3'
    ).setScale(0);

    this.countdownPanel.add(countdownImage);

    // Store reference to the image for animation
    this.countdownPanel.setData('image', countdownImage);
  }

  /**
   * Start the game countdown sequence
   */
  private async startCountdown(): Promise<void> {
    const countdownImages = ['countdown-3', 'countdown-2', 'countdown-1', 'countdown-go'];
    const countdownImage = this.countdownPanel.getData('image');

    for (let i = 0; i < countdownImages.length; i++) {
      await this.playCountdownStep(countdownImage, countdownImages[i], i === countdownImages.length - 1);
    }

    this.countdownPanel.visible = false;
    this.gamePanel.visible = true;

    console.log('Game panel visibility:', this.gamePanel.visible);
    console.log('Game started. Panel should be visible now.');

    // Start the game timer
    this.startGameTimer();

    // Set up initial round
    this.setupRound();
  }

  /**
   * Play a single step of the countdown animation
   */
  private playCountdownStep(image: Phaser.GameObjects.Image, texture: string, isGo: boolean): Promise<void> {
    return new Promise((resolve) => {
      image.setTexture(texture);
      image.setScale(0);

      try {
        this.sound.play(isGo ? 'go' : 'countdown');
      } catch (error) {
        console.warn('Sound playback failed:', error);
      }

      this.tweens.add({
        targets: image,
        scale: 0.2,
        duration: 300,
        ease: 'Back.easeOut',
        onComplete: () => {
          this.time.delayedCall(700, () => {
            this.tweens.add({
              targets: image,
              scale: 0,
              duration: 300,
              ease: 'Back.easeIn',
              onComplete: () => resolve()
            });
          });
        }
      });
    });
  }

  private createUI(): void {
    const { width, height } = this.cameras.main;

    this.UIContainer = this.add.container(0, 0);
    this.gamePanel.add(this.UIContainer);

    // Create timer bar UI
    this.timerBarUI.create(this.UIContainer);

    // Create timer text at the right circle position
    const rightCirclePos = this.timerBarUI.getRightCirclePosition();
    this.timerManager.createUI(rightCirclePos.x, rightCirclePos.y, this.UIContainer);

    // Create score display
    const timerBarY = height * 0.07;
    this.scoreManager.createUI(width / 2, timerBarY + 120, this.UIContainer);

    // Create lives display
    this.livesManager.createUI(width / 2, timerBarY + 50, this.UIContainer);
  

    this.createCenter();

    this.createRoundTimerUI();
  }

  private createCenter(){
    // Option cards - in a plus-shaped pattern with arrows pointing to the center
    const cardOffset = 110; // Reduced distance from center
    const centerX = this.cameras.main.width / 2;
    const centerY = this.cameras.main.height * 0.55;

    // Card positions in plus-shaped pattern with arrows pointing to center
    const cardPositions = [
      { x: centerX - cardOffset, y: centerY - cardOffset }, // Top left
      { x: centerX + cardOffset, y: centerY - cardOffset }, // Top right
      { x: centerX, y: centerY }, // Center
      { x: centerX - cardOffset, y: centerY + cardOffset }, // Bottom left
      { x: centerX + cardOffset, y: centerY + cardOffset }  // Bottom right
    ];

    // Create all cards first
    for (let i = 0; i < GAME_CONFIG.OPTION_CARDS_COUNT; i++) {
      // Create the card
      const card = new MatchingCard(
        this,
        cardPositions[i].x,
        cardPositions[i].y,
        GAME_CONFIG.CARD_SIZE
      );

      // Store card in array but don't add to gamePanel yet
      this.optionCards.push(card);

      // Only add click handler to non-center cards
      if (i !== 2) {
        card.on('pointerdown', () => {
          this.checkAnswer(i);
        });
      }
    }

    // Now add cards to the gamePanel in the correct order (bottom to top)
    // First add all corner cards
    for (let i = 0; i < GAME_CONFIG.OPTION_CARDS_COUNT; i++) {
      if (i !== 2) { // Skip center card for now
        this.gamePanel.add(this.optionCards[i]);
        this.optionCards[i].setDepth(1); // Set lower depth for outer cards
      }
    }

    // Now add center card with special styling
    const centerCard = this.optionCards[2];

    // Add a solid backdrop behind the center card to block visibility of cards underneath
    // Use Graphics for rounded rectangle since Rectangle doesn't support rounded corners
    const solidBackdrop = this.add.graphics();
    solidBackdrop.fillStyle(0x181818, 1); // Dark color matching the background

    // Make backdrop slightly smaller to ensure proper coverage
    const backdropSize = GAME_CONFIG.CARD_SIZE * 0.9;
    solidBackdrop.fillRoundedRect(
      cardPositions[2].x - backdropSize/2,
      cardPositions[2].y - backdropSize/2,
      backdropSize,
      backdropSize,
      16 // Corner radius
    );
    this.gamePanel.add(solidBackdrop);
    solidBackdrop.setDepth(8); // Behind the glow but above other cards

    // Add glowing border effect to the center card
    const borderGlow = this.add.image(cardPositions[2].x, cardPositions[2].y, 'card_bg')
      .setOrigin(0.5)
      .setDisplaySize(GAME_CONFIG.CARD_SIZE + 5, GAME_CONFIG.CARD_SIZE + 5) // Reduced size difference (was +10)
      .setTint(0x33DDFF) // Cyan color matching the timer
      .setAlpha(0.4); // Reduced from 0.8 to 0.4 (50% decrease)

    // // Add glow effect with post-processing if WebGL is available
    // if (this.sys.game.renderer.type === Phaser.WEBGL) {
    //   borderGlow.setBlendMode(Phaser.BlendModes.ADD);
    // }

    // Add border glow first, then center card
    this.gamePanel.add(borderGlow);
    borderGlow.setDepth(9); // Just below the card but above other cards

    // Finally add center card on top of everything
    this.gamePanel.add(centerCard);
    centerCard.setDepth(10); // Higher depth value so it renders on top

    // Center card fully opaque
    centerCard.getCardBackground().setAlpha(1);
    
    // Disable interactivity on center card since it's the reference
    centerCard.disableInteractive();
  }

  private createRoundTimerUI(): void {
    const { width, height } = this.cameras.main;

    // Create the radial timer UI
    this.radialTimerUI = new RadialTimerUI(this, {
      x: width / 2,
      y: height * 0.55,
      size: GAME_CONFIG.CARD_SIZE * 3,
      cornerRadius: 16,
      borderWidth: 8,
    });

    // Create and add to game panel
    this.radialTimerUI.create();
    this.radialTimerUI.setDepth(7); // Behind the center card backdrop but above other cards

    // Add to game panel
    const container = this.radialTimerUI.getContainer();
    if (container) {
      this.gamePanel.add(container);
    }

    // Initialize with full progress
    this.radialTimerUI.updateProgress(1.0);
  }

  private updateRadialTimer(progress: number): void {
    if (!this.radialTimerUI) return;

    // Update the radial timer UI with the current progress
    this.radialTimerUI.updateProgress(progress);
  }

  private startGameTimer(): void {
    this.timerManager.start();
  }

  private setupRound(): void {
    // Play round start sound
    this.sound.play('round');

    // Reset lock state
    this.isLocked = false;

    // Start round timer
    this.currentRoundTime = this.roundTime;
    this.startRoundTimer();

    // Choose a random animal and color for the correct card
    // Step 1: First, choose which animal we'll use for the correct answer
    const correctAnimalIndex = this.rng.between(0, ANIMAL_NAMES.length - 1);
    const correctAnimal = ANIMAL_NAMES[correctAnimalIndex];

    // Step 2: Choose which color variation of this animal to use
    const correctColorCategory = this.rng.between(0, COLOR_NAMES.length - 1);
    const correctColor = COLOR_NAMES[correctColorCategory];

    // The main image is the hidden one that we're matching against
    // It will be used by the logic behind the scenes, but not displayed
    const mainImageKey = this.getAnimalImageKey(correctAnimalIndex, correctColorCategory);
    this.mainImage?.setTexture(mainImageKey);

    // Set appropriate tint based on color category

    // Log the selected animal and color for debugging
    console.log(`Main image: ${correctAnimal} in ${correctColor} (indices: ${correctAnimalIndex}, ${correctColorCategory})`);

    // The center card (index 2) should always be the guide card (matching distractor)
    // It should never be the correct card to click
    const matchingImageCardIndex = 2; // Center position is always the matching distractor

    // Choose a random position for the correct card (not center)
    do {
      this.correctCardIndex = this.rng.between(0, 4);
    } while (this.correctCardIndex === 2); // Avoid center position

    // Set up the 5 option cards
    // Create a list of all possible animal indices excluding the correct animal
    const availableAnimalIndices = Array.from(
      { length: ANIMAL_NAMES.length },
      (_, index) => index
    ).filter(index => index !== correctAnimalIndex);

    // Shuffle the available animal indices to pick random ones
    this.rng.shuffle(availableAnimalIndices);

    // Keep track of which animals we've assigned to cards
    const usedAnimalIndices = new Set<number>();
    usedAnimalIndices.add(correctAnimalIndex); // Mark the correct animal as used

    for (let i = 0; i < this.optionCards.length; i++) {
      // Reset card selection state
      this.optionCards[i].resetSelection();

      if (i === this.correctCardIndex) {
        // This is the correct card - use the same animal as main image but with a DIFFERENT color
        // Choose a different color than the one used for the center
        let cardColorCategory;
        do {
          cardColorCategory = this.rng.between(0, COLOR_NAMES.length - 1);
        } while (cardColorCategory === correctColorCategory);

        const cardColor = COLOR_NAMES[cardColorCategory];
        const cardImageKey = this.getAnimalImageKey(correctAnimalIndex, cardColorCategory);

        console.log(`Correct card (${i}): ${correctAnimal} in ${cardColor}`);

        this.optionCards[i].setCardImage(cardImageKey);
        this.optionCards[i].setCorrect(true);

        // Set the tint based on this card's color category
        this.optionCards[i].setTint(CATEGORY_TINTS[cardColorCategory]);

        // // Add glow effect to the image if WebGL available
        // if (this.sys.game.renderer.type === Phaser.WEBGL) {
        //   // Access the card image through the container
        //   const cardImage = this.optionCards[i].getAt(1) as Phaser.GameObjects.Image;
        //   if (cardImage) {
        //     cardImage.setPipeline('Light2D');
        //     // Increase intensity for more prominent glow
        //     const lightColor = CATEGORY_TINTS[cardColorCategory];
        //     // Use the card's position directly instead of the positions array
        //     this.lights.addLight(this.optionCards[i].x, this.optionCards[i].y, GAME_CONFIG.CARD_SIZE * 0.9, lightColor, 1.8);
        //   }
        // }
      } else if (i === matchingImageCardIndex) {
        // This distractor card should match the main image exactly (same animal, same color)
        const matchingImageKey = this.getAnimalImageKey(correctAnimalIndex, correctColorCategory);

        console.log(`Matching distractor card (${i}): ${correctAnimal} in ${correctColor} (exact match to main)`);

        this.optionCards[i].setCardImage(matchingImageKey);
        this.optionCards[i].setCorrect(false);

        // Set the tint based on the main image's color category
        this.optionCards[i].setTint(CATEGORY_TINTS[correctColorCategory]);

        // // Add glow effect to the image if WebGL available
        // if (this.sys.game.renderer.type === Phaser.WEBGL) {
        //   // Access the card image through the container
        //   const cardImage = this.optionCards[i].getAt(1) as Phaser.GameObjects.Image;
        //   if (cardImage) {
        //     cardImage.setPipeline('Light2D');
        //     // Increase intensity for more prominent glow
        //     const lightColor = CATEGORY_TINTS[correctColorCategory];
        //     // Use the card's position directly
        //     this.lights.addLight(this.optionCards[i].x, this.optionCards[i].y, GAME_CONFIG.CARD_SIZE * 0.9, lightColor, 1.8);
        //   }
        // }
      } else {
        // This is a regular distractor card - use a different animal
        // Get an animal index we haven't used yet
        // If we've used all available indices, reuse one (this can happen with the last card)
        const distractorAnimalIndex = availableAnimalIndices.length > 0 
          ? availableAnimalIndices.pop()! 
          : Array.from(usedAnimalIndices).find(index => index !== correctAnimalIndex) || 0;

        // Only add to used indices if it's not already there
        if (!usedAnimalIndices.has(distractorAnimalIndex)) {
          usedAnimalIndices.add(distractorAnimalIndex);
        }

        const distractorAnimal = ANIMAL_NAMES[distractorAnimalIndex];

        // Choose a random color for this distractor
        const distractorColorCategory = this.rng.between(0, COLOR_NAMES.length - 1);
        const distractorColor = COLOR_NAMES[distractorColorCategory];

        const distractorImageKey = this.getAnimalImageKey(distractorAnimalIndex, distractorColorCategory);

        console.log(`Regular distractor card (${i}): ${distractorAnimal} in ${distractorColor}`);

        this.optionCards[i].setCardImage(distractorImageKey);
        this.optionCards[i].setCorrect(false);

        // Set the tint based on this card's color category and add glow effect
        this.optionCards[i].setTint(CATEGORY_TINTS[distractorColorCategory]);
        // // Add additional glow effect to the image if WebGL available
        // if (this.sys.game.renderer.type === Phaser.WEBGL) {
        //   // Access the card image through the container
        //   const cardImage = this.optionCards[i].getAt(1) as Phaser.GameObjects.Image;
        //   if (cardImage) {
        //     cardImage.setPipeline('Light2D');
        //     // Increase intensity for more prominent glow
        //     const lightColor = CATEGORY_TINTS[distractorColorCategory];
        //     // Use the card's position directly
        //     this.lights.addLight(this.optionCards[i].x, this.optionCards[i].y, GAME_CONFIG.CARD_SIZE * 0.9, lightColor, 1.8);
        //   }
        // }
      }

      // Animate card image appearing with delay
      this.optionCards[i].animateCardImage(200 + i * 50);
    }
  }

  private startRoundTimer(): void {
    // Clear any existing timer
    if (this.roundTimer) {
      this.roundTimer.remove();
    }

    // Create a new timer that updates 100 times per second for smooth animation
    this.roundTimer = this.time.addEvent({
      delay: 10,
      callback: this.updateRoundTimer,
      callbackScope: this,
      repeat: this.roundTime * 100 - 1
    });
  }

  private updateRoundTimer(): void {
    if (this.isLocked || this.isGameOver) return;

    // Decrease timer by 0.01 seconds
    // Round to 2 decimal places to avoid floating point precision issues
    this.currentRoundTime = Math.max(0, Math.round((this.currentRoundTime - 0.01) * 100) / 100);

    // Calculate percentage of time remaining
    const percentage = this.currentRoundTime / this.roundTime;

    // Update the radial timer with current progress
    this.updateRadialTimer(percentage);

    // Check if round time is up
    if (this.currentRoundTime <= 0) {
      // Clear the timer to avoid multiple calls
      if (this.roundTimer) {
        this.roundTimer.remove();
      }

      // Automatically move to next round if time runs out
      this.time.delayedCall(500, this.setupRound, [], this);
    }
  }

  private checkAnswer(index: number): void {
    // Ignore if game is locked or over
    if (this.isLocked || this.isGameOver) return;

    // Lock the game temporarily
    this.isLocked = true;

    if (index === this.correctCardIndex) {
      // Correct answer
      this.sound.play('correct');

      // Mark the card as correct
      this.optionCards[index].markSelected(true);

      // Calculate score bonus based on time remaining
      const percentage = Math.floor((this.currentRoundTime / this.roundTime) * 100);

      // Update score
      // this.score += percentage;

      // // Check if scoreText exists before updating it
      // if (this.scoreText) {
      //   this.scoreText.setText(this.score.toString());
      // }

      this.scoreManager.addPoints(percentage);

      // Show bonus animation with message
      this.showBonusText(`Good Choice +${percentage}`, true);

      // Clear the round timer to prevent it from continuing to tick down
      if (this.roundTimer) {
        this.roundTimer.remove();
      }

      // Set up next round after delay
      this.time.delayedCall(780, this.setupRound, [], this);
    } else {
      // Wrong answer
      this.sound.play('wrong');

      // Mark the card as wrong
      this.optionCards[index].markSelected(false);

      // Penalty for wrong answer
      const penalty = 20;
      // this.score = Math.max(0, this.score - penalty);

      // // Check if scoreText exists before updating it
      // if (this.scoreText) {
      //   this.scoreText.setText(this.score.toString());
      // }

      this.scoreManager.subtractPoints(penalty);

      this.livesManager.deductHeart(
        this.cameras.main.width / 2,
        this.cameras.main.height / 2
      );

      // Show penalty animation with message
      this.showBonusText(`Bad Choice -${penalty}`, false);

      // Enable selecting another card after a short delay
      this.time.delayedCall(500, () => {
        this.isLocked = false;
      });
    }
  }

  private showBonusText(text: string, isCorrect: boolean): void {
    // Create or reuse text object
    if (!this.bonusScoreText || !(this.bonusScoreText instanceof Phaser.GameObjects.Text)) {
      // Create text with the appropriate style
      this.bonusScoreText = this.add.text(
        this.cameras.main.width / 2,
        this.cameras.main.height / 2.5,
        text,
        {
          fontFamily: 'Arial',
          fontSize: '32px',
          fontStyle: 'italic',
          color: isCorrect ? '#4FFFAA' : '#FF4F59',
          stroke: '#000000',
          strokeThickness: 3,
          shadow: { offsetX: 1, offsetY: 1, color: '#000000', blur: 2, stroke: true, fill: true }
        }
      ).setOrigin(0.5).setDepth(100).setAlpha(0);

      this.gamePanel.add(this.bonusScoreText);
    } else {
      // Update existing text
      this.bonusScoreText.setText(text);
      this.bonusScoreText.setColor(isCorrect ? '#4FFFAA' : '#FF4F59');
      this.bonusScoreText.setPosition(this.cameras.main.width / 2, this.cameras.main.height / 2.5);
    }

    // Animation sequence
    const targetY = this.cameras.main.height / 2.5 - 50; // Move upward

    // Clear any existing tweens
    this.tweens.killTweensOf(this.bonusScoreText);

    // Reset position and alpha
    this.bonusScoreText.setAlpha(0);
    this.bonusScoreText.setPosition(this.cameras.main.width / 2, this.cameras.main.height / 2.5);

    // Create fade in tween
    this.tweens.add({
      targets: this.bonusScoreText,
      alpha: 1,
      duration: 200,
      ease: 'Linear'
    });

    // Create move up tween
    this.tweens.add({
      targets: this.bonusScoreText,
      y: targetY,
      duration: 700,
      ease: 'Cubic.easeOut'
    });

    // Create fade out tween with delay
    this.tweens.add({
      targets: this.bonusScoreText,
      alpha: 0,
      delay: 600,
      duration: 300,
      ease: 'Linear'
    });
  }

  /**
   * Get the image key for a specific animal and color
   * @param animalIndex The index of the animal
   * @param colorIndex The index of the color
   * @returns The image key
   */
  private getAnimalImageKey(animalIndex: number, colorIndex: number): string {
    // Check if the 2D array is properly initialized
    if (this.animalImages && 
        animalIndex >= 0 && animalIndex < this.animalImages.length &&
        colorIndex >= 0 && colorIndex < this.animalImages[animalIndex].length) {
      return this.animalImages[animalIndex][colorIndex];
    }

    // Fallback to the old format if the 2D array is not available
    console.warn(`Using fallback image key for animal ${animalIndex}, color ${colorIndex}`);
    return `image_${colorIndex}_${animalIndex}`;
  }

  private endGame(): void {
    if (this.isGameOver) return;

    // Set game over flag
    this.isGameOver = true;

    if (this.roundTimer) {
      this.roundTimer.remove();
    }

    // Play end game sound
    this.sound.play('end');

    // Transition to end scene after a short delay
    this.time.delayedCall(500, () => {
      this.scene.start('GameEndScene', { score: this.scoreManager.getScore() });
    });
  }
}
