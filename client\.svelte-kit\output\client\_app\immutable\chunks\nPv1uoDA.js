import{T as sr,m as ur,b as k,s as F,h as O,j as Tr,a as Nr,B as W,a1 as Sr,r as Ir,H as Or,k as m,l as $,i as R,as as wr,ax as Cr,f as V,p as Mr,o as Lr,aG as x,X as Hr,aH as rr,aB as lr,ag as nr,aI as y,q as or,u as Rr,aJ as q,aK as Dr,aL as G,g as B,au as Pr,aM as $r,aA as Vr,aN as Br,a4 as Ur,aO as vr,aP as Yr,R as cr,aQ as yr,aR as qr,t as Kr,aS as Xr,aT as Fr,aU as Gr,aV as zr,aW as Jr,aX as Qr}from"./UXlYzocm.js";import{i as Wr,c as jr,d as Zr,n as kr,b as mr}from"./AD_Ai2mn.js";function xr(r,i){if(i){const e=document.body;r.autofocus=!0,sr(()=>{document.activeElement===e&&r.focus()})}}function _e(r,i){return i}function re(r,i,e){for(var f=r.items,a=[],t=i.length,s=0;s<t;s++)$r(i[s].e,a,!0);var o=t>0&&a.length===0&&e!==null;if(o){var E=e.parentNode;Vr(E),E.append(e),f.clear(),C(r,i[0].prev,i[t-1].next)}Br(a,()=>{for(var c=0;c<t;c++){var g=i[c];o||(f.delete(g.k),C(r,g.prev,g.next)),B(g.e,!o)}})}function he(r,i,e,f,a,t=null){var s=r,o={flags:i,items:new Map,first:null},E=(i&vr)!==0;if(E){var c=r;s=O?F(Tr(c)):c.appendChild(ur())}O&&Nr();var g=null,T=!1,h=new Map,I=Sr(()=>{var p=e();return nr(p)?p:p==null?[]:lr(p)}),n,v;function A(){ee(v,n,o,h,s,a,i,f,e),t!==null&&(n.length===0?g?or(g):g=V(()=>t(s)):g!==null&&Rr(g,()=>{g=null}))}k(()=>{v??=Ur,n=W(I);var p=n.length;if(T&&p===0)return;T=p===0;let N=!1;if(O){var d=Ir(s)===Or;d!==(p===0)&&(s=m(),F(s),$(!1),N=!0)}if(O){for(var _=null,b,u=0;u<p;u++){if(R.nodeType===wr&&R.data===Cr){s=R,N=!0,$(!1);break}var l=n[u],S=f(l,u);b=j(R,o,_,null,l,S,u,a,i,e),o.items.set(S,b),_=b}p>0&&F(m())}if(O)p===0&&t&&(g=V(()=>t(s)));else if(Mr()){var M=new Set,U=Lr;for(u=0;u<p;u+=1){l=n[u],S=f(l,u);var L=o.items.get(S)??h.get(S);L?(i&(q|y))!==0&&dr(L,l,u,i):(b=j(null,o,null,null,l,S,u,a,i,e,!0),h.set(S,b)),M.add(S)}for(const[w,Y]of o.items)M.has(w)||U.skipped_effects.add(Y.e);U.add_callback(A)}else A();N&&$(!0),W(I)}),O&&(s=R)}function ee(r,i,e,f,a,t,s,o,E){var c=(s&Yr)!==0,g=(s&(q|y))!==0,T=i.length,h=e.items,I=e.first,n=I,v,A=null,p,N=[],d=[],_,b,u,l;if(c)for(l=0;l<T;l+=1)_=i[l],b=o(_,l),u=h.get(b),u!==void 0&&(u.a?.measure(),(p??=new Set).add(u));for(l=0;l<T;l+=1){if(_=i[l],b=o(_,l),u=h.get(b),u===void 0){var S=f.get(b);if(S!==void 0){f.delete(b),h.set(b,S);var M=A?A.next:n;C(e,A,S),C(e,S,M),z(S,M,a),A=S}else{var U=n?n.e.nodes_start:a;A=j(U,e,A,A===null?e.first:A.next,_,b,l,t,s,E)}h.set(b,A),N=[],d=[],n=A.next;continue}if(g&&dr(u,_,l,s),(u.e.f&G)!==0&&(or(u.e),c&&(u.a?.unfix(),(p??=new Set).delete(u))),u!==n){if(v!==void 0&&v.has(u)){if(N.length<d.length){var L=d[0],w;A=L.prev;var Y=N[0],K=N[N.length-1];for(w=0;w<N.length;w+=1)z(N[w],L,a);for(w=0;w<d.length;w+=1)v.delete(d[w]);C(e,Y.prev,K.next),C(e,A,Y),C(e,K,L),n=L,A=K,l-=1,N=[],d=[]}else v.delete(u),z(u,n,a),C(e,u.prev,u.next),C(e,u,A===null?e.first:A.next),C(e,A,u),A=u;continue}for(N=[],d=[];n!==null&&n.k!==b;)(n.e.f&G)===0&&(v??=new Set).add(n),d.push(n),n=n.next;if(n===null)continue;u=n}N.push(u),A=u,n=u.next}if(n!==null||v!==void 0){for(var H=v===void 0?[]:lr(v);n!==null;)(n.e.f&G)===0&&H.push(n),n=n.next;var X=H.length;if(X>0){var gr=(s&vr)!==0&&T===0?a:null;if(c){for(l=0;l<X;l+=1)H[l].a?.measure();for(l=0;l<X;l+=1)H[l].a?.fix()}re(e,H,gr)}}c&&sr(()=>{if(p!==void 0)for(u of p)u.a?.apply()}),r.first=e.first&&e.first.e,r.last=A&&A.e;for(var Er of f.values())B(Er.e);f.clear()}function dr(r,i,e,f){(f&q)!==0&&x(r.v,i),(f&y)!==0?x(r.i,e):r.i=e}function j(r,i,e,f,a,t,s,o,E,c,g){var T=(E&q)!==0,h=(E&Dr)===0,I=T?h?Hr(a,!1,!1):rr(a):a,n=(E&y)===0?s:rr(s),v={i:n,v:I,k:t,a:null,e:null,prev:e,next:f};try{if(r===null){var A=document.createDocumentFragment();A.append(r=ur())}return v.e=V(()=>o(r,I,n,c),O),v.e.prev=e&&e.e,v.e.next=f&&f.e,e===null?g||(i.first=v):(e.next=v,e.e.next=v.e),f!==null&&(f.prev=v,f.e.prev=v.e),v}finally{}}function z(r,i,e){for(var f=r.next?r.next.e.nodes_start:e,a=i?i.e.nodes_start:e,t=r.e.nodes_start;t!==null&&t!==f;){var s=Pr(t);a.before(t),t=s}}function C(r,i,e){i===null?r.first=e:(i.next=e,i.e.next=e&&e.e),e!==null&&(e.prev=i,e.e.prev=i&&i.e)}function ie(r,i){var e=void 0,f;k(()=>{e!==(e=i())&&(f&&(B(f),f=null),e&&(f=V(()=>{cr(()=>e(r))})))})}function _r(r){var i,e,f="";if(typeof r=="string"||typeof r=="number")f+=r;else if(typeof r=="object")if(Array.isArray(r)){var a=r.length;for(i=0;i<a;i++)r[i]&&(e=_r(r[i]))&&(f&&(f+=" "),f+=e)}else for(e in r)r[e]&&(f&&(f+=" "),f+=e);return f}function fe(){for(var r,i,e=0,f="",a=arguments.length;e<a;e++)(r=arguments[e])&&(i=_r(r))&&(f&&(f+=" "),f+=i);return f}function ae(r){return typeof r=="object"?fe(r):r??""}const er=[...` 	
\r\f \v\uFEFF`];function te(r,i,e){var f=r==null?"":""+r;if(i&&(f=f?f+" "+i:i),e){for(var a in e)if(e[a])f=f?f+" "+a:a;else if(f.length)for(var t=a.length,s=0;(s=f.indexOf(a,s))>=0;){var o=s+t;(s===0||er.includes(f[s-1]))&&(o===f.length||er.includes(f[o]))?f=(s===0?"":f.substring(0,s))+f.substring(o+1):s=o}}return f===""?null:f}function ir(r,i=!1){var e=i?" !important;":";",f="";for(var a in r){var t=r[a];t!=null&&t!==""&&(f+=" "+a+": "+t+e)}return f}function J(r){return r[0]!=="-"||r[1]!=="-"?r.toLowerCase():r}function se(r,i){if(i){var e="",f,a;if(Array.isArray(i)?(f=i[0],a=i[1]):f=i,r){r=String(r).replaceAll(/\s*\/\*.*?\*\/\s*/g,"").trim();var t=!1,s=0,o=!1,E=[];f&&E.push(...Object.keys(f).map(J)),a&&E.push(...Object.keys(a).map(J));var c=0,g=-1;const v=r.length;for(var T=0;T<v;T++){var h=r[T];if(o?h==="/"&&r[T-1]==="*"&&(o=!1):t?t===h&&(t=!1):h==="/"&&r[T+1]==="*"?o=!0:h==='"'||h==="'"?t=h:h==="("?s++:h===")"&&s--,!o&&t===!1&&s===0){if(h===":"&&g===-1)g=T;else if(h===";"||T===v-1){if(g!==-1){var I=J(r.substring(c,g).trim());if(!E.includes(I)){h!==";"&&T++;var n=r.substring(c,T).trim();e+=" "+n+";"}}c=T+1,g=-1}}}}return f&&(e+=ir(f)),a&&(e+=ir(a,!0)),e=e.trim(),e===""?null:e}return r==null?null:String(r)}function ue(r,i,e,f,a,t){var s=r.__className;if(O||s!==e||s===void 0){var o=te(e,f,t);(!O||o!==r.getAttribute("class"))&&(o==null?r.removeAttribute("class"):i?r.className=o:r.setAttribute("class",o)),r.__className=e}else if(t&&a!==t)for(var E in t){var c=!!t[E];(a==null||c!==!!a[E])&&r.classList.toggle(E,c)}return t}function Q(r,i={},e,f){for(var a in e){var t=e[a];i[a]!==t&&(e[a]==null?r.style.removeProperty(a):r.style.setProperty(a,t,f))}}function le(r,i,e,f){var a=r.__style;if(O||a!==i){var t=se(i,f);(!O||t!==r.getAttribute("style"))&&(t==null?r.removeAttribute("style"):r.style.cssText=t),r.__style=i}else f&&(Array.isArray(f)?(Q(r,e?.[0],f[0]),Q(r,e?.[1],f[1],"important")):Q(r,e,f));return f}function Z(r,i,e=!1){if(r.multiple){if(i==null)return;if(!nr(i))return yr();for(var f of r.options)f.selected=i.includes(fr(f));return}for(f of r.options){var a=fr(f);if(qr(a,i)){f.selected=!0;return}}(!e||i!==void 0)&&(r.selectedIndex=-1)}function ne(r){var i=new MutationObserver(()=>{Z(r,r.__value)});i.observe(r,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["value"]}),Kr(()=>{i.disconnect()})}function fr(r){return"__value"in r?r.__value:r.value}const D=Symbol("class"),P=Symbol("style"),hr=Symbol("is custom element"),pr=Symbol("is html");function oe(r,i){i?r.hasAttribute("selected")||r.setAttribute("selected",""):r.removeAttribute("selected")}function ar(r,i,e,f){var a=br(r);O&&(a[i]=r.getAttribute(i),i==="src"||i==="srcset"||i==="href"&&r.nodeName==="LINK")||a[i]!==(a[i]=e)&&(i==="loading"&&(r[Gr]=e),e==null?r.removeAttribute(i):typeof e!="string"&&Ar(r).includes(i)?r[i]=e:r.setAttribute(i,e))}function ve(r,i,e,f,a=!1){var t=br(r),s=t[hr],o=!t[pr];let E=O&&s;E&&$(!1);var c=i||{},g=r.tagName==="OPTION";for(var T in i)T in e||(e[T]=null);e.class?e.class=ae(e.class):e[D]&&(e.class=null),e[P]&&(e.style??=null);var h=Ar(r);for(const d in e){let _=e[d];if(g&&d==="value"&&_==null){r.value=r.__value="",c[d]=_;continue}if(d==="class"){var I=r.namespaceURI==="http://www.w3.org/1999/xhtml";ue(r,I,_,f,i?.[D],e[D]),c[d]=_,c[D]=e[D];continue}if(d==="style"){le(r,_,i?.[P],e[P]),c[d]=_,c[P]=e[P];continue}var n=c[d];if(!(_===n&&!(_===void 0&&r.hasAttribute(d)))){c[d]=_;var v=d[0]+d[1];if(v!=="$$")if(v==="on"){const b={},u="$$"+d;let l=d.slice(2);var A=mr(l);if(Wr(l)&&(l=l.slice(0,-7),b.capture=!0),!A&&n){if(_!=null)continue;r.removeEventListener(l,c[u],b),c[u]=null}if(_!=null)if(A)r[`__${l}`]=_,Zr([l]);else{let S=function(M){c[d].call(this,M)};c[u]=jr(l,r,S,b)}else A&&(r[`__${l}`]=void 0)}else if(d==="style")ar(r,d,_);else if(d==="autofocus")xr(r,!!_);else if(!s&&(d==="__value"||d==="value"&&_!=null))r.value=r.__value=_;else if(d==="selected"&&g)oe(r,_);else{var p=d;o||(p=kr(p));var N=p==="defaultValue"||p==="defaultChecked";if(_==null&&!s&&!N)if(t[d]=null,p==="value"||p==="checked"){let b=r;const u=i===void 0;if(p==="value"){let l=b.defaultValue;b.removeAttribute(p),b.defaultValue=l,b.value=b.__value=u?l:null}else{let l=b.defaultChecked;b.removeAttribute(p),b.defaultChecked=l,b.checked=u?l:!1}}else r.removeAttribute(d);else N||h.includes(p)&&(s||typeof _!="string")?r[p]=_:typeof _!="function"&&ar(r,p,_)}}}return E&&$(!0),c}function pe(r,i,e=[],f=[],a,t=!1){Jr(e,f,s=>{var o=void 0,E={},c=r.nodeName==="SELECT",g=!1;if(k(()=>{var h=i(...s.map(W)),I=ve(r,o,h,a,t);g&&c&&"value"in h&&Z(r,h.value);for(let v of Object.getOwnPropertySymbols(E))h[v]||B(E[v]);for(let v of Object.getOwnPropertySymbols(h)){var n=h[v];v.description===Qr&&(!o||n!==o[v])&&(E[v]&&B(E[v]),E[v]=V(()=>ie(r,()=>n))),I[v]=n}o=I}),c){var T=r;cr(()=>{Z(T,o.value,!0),ne(T)})}g=!0})}function br(r){return r.__attributes??={[hr]:r.nodeName.includes("-"),[pr]:r.namespaceURI===Xr}}var tr=new Map;function Ar(r){var i=tr.get(r.nodeName);if(i)return i;tr.set(r.nodeName,i=[]);for(var e,f=r,a=Element.prototype;a!==f;){e=zr(f);for(var t in e)e[t].set&&i.push(t);f=Fr(f)}return i}export{ue as a,le as b,pe as c,he as e,_e as i,ar as s};
