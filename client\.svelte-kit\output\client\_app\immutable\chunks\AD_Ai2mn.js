import{ap as E,aq as T,af as I,a4 as C,W as F,ag as H,T as q,m as V,b as Y,ar as z,h as w,as as S,at as M,au as L,l as g,s as k,i as _,j as P,av as O,aw as N,a as $,ax as G,ay as U,az as J,aA as K,aB as Q,aC as X,f as Z,F as ee,v as te,d as ae,L as re}from"./UXlYzocm.js";function fe(e){return e.endsWith("capture")&&e!=="gotpointercapture"&&e!=="lostpointercapture"}const ne=["beforeinput","click","change","dblclick","contextmenu","focusin","focusout","input","keydown","keyup","mousedown","mousemove","mouseout","mouseover","mouseup","pointerdown","pointermove","pointerout","pointerover","pointerup","touchend","touchmove","touchstart"];function _e(e){return ne.includes(e)}const oe={formnovalidate:"formNoValidate",ismap:"isMap",nomodule:"noModule",playsinline:"playsInline",readonly:"readOnly",defaultvalue:"defaultValue",defaultchecked:"defaultChecked",srcobject:"srcObject",novalidate:"noValidate",allowfullscreen:"allowFullscreen",disablepictureinpicture:"disablePictureInPicture",disableremoteplayback:"disableRemotePlayback"};function he(e){return e=e.toLowerCase(),oe[e]??e}const ie=["touchstart","touchmove"];function se(e){return ie.includes(e)}function ue(e){var t=I,r=C;E(null),T(null);try{return e()}finally{E(t),T(r)}}const x=new Set,D=new Set;function pe(e,t,r,i={}){function s(a){if(i.capture||b.call(t,a),!a.cancelBubble)return ue(()=>r?.call(this,a))}return e.startsWith("pointer")||e.startsWith("touch")||e==="wheel"?q(()=>{t.addEventListener(e,s,i)}):t.addEventListener(e,s,i),s}function ve(e){for(var t=0;t<e.length;t++)x.add(e[t]);for(var r of D)r(e)}function b(e){var t=this,r=t.ownerDocument,i=e.type,s=e.composedPath?.()||[],a=s[0]||e.target,c=0,h=e.__root;if(h){var d=s.indexOf(h);if(d!==-1&&(t===document||t===window)){e.__root=t;return}var p=s.indexOf(t);if(p===-1)return;d<=p&&(c=d)}if(a=s[c]||e.target,a!==t){F(e,"currentTarget",{configurable:!0,get(){return a||r}});var A=I,l=C;E(null),T(null);try{for(var n,o=[];a!==null;){var v=a.assignedSlot||a.parentNode||a.host||null;try{var f=a["__"+i];if(f!=null&&(!a.disabled||e.target===a))if(H(f)){var[j,...B]=f;j.apply(a,[e,...B])}else f.call(a,e)}catch(m){n?o.push(m):n=m}if(e.cancelBubble||v===t||v===null)break;a=v}if(n){for(let m of o)queueMicrotask(()=>{throw m});throw n}}finally{e.__root=t,delete e.currentTarget,E(A),T(l)}}}let u;function le(){u=void 0}function ye(e){let t=null,r=w;var i;if(w){for(t=_,u===void 0&&(u=P(document.head));u!==null&&(u.nodeType!==S||u.data!==M);)u=L(u);u===null?g(!1):u=k(L(u))}w||(i=document.head.appendChild(V()));try{Y(()=>e(i),z)}finally{r&&(g(!0),u=_,k(t))}}function ge(e,t){var r=t==null?"":typeof t=="object"?t+"":t;r!==(e.__t??=e.nodeValue)&&(e.__t=r,e.nodeValue=r+"")}function ce(e,t){return W(e,t)}function we(e,t){O(),t.intro=t.intro??!1;const r=t.target,i=w,s=_;try{for(var a=P(r);a&&(a.nodeType!==S||a.data!==M);)a=L(a);if(!a)throw N;g(!0),k(a),$();const c=W(e,{...t,anchor:a});if(_===null||_.nodeType!==S||_.data!==G)throw U(),N;return g(!1),c}catch(c){if(c===N)return t.recover===!1&&J(),O(),K(r),g(!1),ce(e,t);throw c}finally{g(i),k(s),le()}}const y=new Map;function W(e,{target:t,anchor:r,props:i={},events:s,context:a,intro:c=!0}){O();var h=new Set,d=l=>{for(var n=0;n<l.length;n++){var o=l[n];if(!h.has(o)){h.add(o);var v=se(o);t.addEventListener(o,b,{passive:v});var f=y.get(o);f===void 0?(document.addEventListener(o,b,{passive:v}),y.set(o,1)):y.set(o,f+1)}}};d(Q(x)),D.add(d);var p=void 0,A=X(()=>{var l=r??t.appendChild(V());return Z(()=>{if(a){ee({});var n=te;n.c=a}s&&(i.$$events=s),w&&ae(l,null),p=e(l,i)||{},w&&(C.nodes_end=_),a&&re()}),()=>{for(var n of h){t.removeEventListener(n,b);var o=y.get(n);--o===0?(document.removeEventListener(n,b),y.delete(n)):y.set(n,o)}D.delete(d),l!==r&&l.parentNode?.removeChild(l)}});return R.set(p,A),p}let R=new WeakMap;function be(e,t){const r=R.get(e);return r?(R.delete(e),r(t)):Promise.resolve()}export{we as a,_e as b,pe as c,ve as d,ye as h,fe as i,ce as m,he as n,ge as s,be as u};
