import Phaser from 'phaser';
import type { LivesConfig } from '../types/types';


export default class LivesManager {
  private scene: Phaser.Scene;
  private config: Required<LivesConfig>;
  private lives: number;
  
  // UI Elements
  private hearts: Phaser.GameObjects.Image[] = [];
  private container?: Phaser.GameObjects.Container;
  
  // Events
  private events: Phaser.Events.EventEmitter;

  constructor(scene: Phaser.Scene, config: LivesConfig = {}) {
    this.scene = scene;
    this.events = new Phaser.Events.EventEmitter();
    
    // Set default configuration
    this.config = {
      initialLives: config.initialLives ?? 3,
    };
    
    this.lives = this.config.initialLives;
  }

  /**
   * Create the lives UI elements at the specified position
   */
  public createUI(x: number, y: number, parentContainer?: Phaser.GameObjects.Container): void {
    this.container = this.scene.add.container(x, y);

    const totalWidth = (this.lives - 1) * 40; // Total width of all hearts
    const startX = -totalWidth / 2; // Start from half the total width to the left

    for (let i = 0; i < this.lives; i++) {
      let heart = this.scene.add.image(
        startX + i * 40,
        0,
        'heart'
      ).setOrigin(0.5).setScale(1.5);
      this.hearts.push(heart);
    }

    // Add to container
    this.container.add(this.hearts);
    
    // Add to parent container if provided
    if (parentContainer) {
      parentContainer.add(this.container);
    }
  }

  deductHeart(fromX?: number, fromY?: number) {
    // console.log('deducting heart');
    this.lives--;
    // console.log('lives', this.lives);
    // console.log('hearts', this.hearts);
    if (this.hearts[this.lives]) {
      // console.log('deducting heart', this.lives);
      this.hearts[this.lives].setTexture('heart_outline');
    }

    // Create flying heart_broken animation if coordinates are provided
    if (fromX !== undefined && fromY !== undefined && this.container) {
      this.createFlyingHeartAnimation(fromX, fromY);
    }

    this.events.emit('heartDeducted', this.lives);
  }

  /**
   * Create animated flying heart_broken image
   */
  private createFlyingHeartAnimation(startX: number, startY: number): void {
    if (!this.container) return;

    // Create the flying heart_broken image
    const flyingHeart = this.scene.add.image(startX, startY, 'heart_broken')
      .setOrigin(0.5)
      .setScale(1.5)
      .setAlpha(0.4);

    // Animate the heart flying to the hearts display
    this.scene.tweens.add({
      targets: flyingHeart,
      y: startY - 200,
      scale: 3.0,
      alpha: 0.8,
      duration: 600,
      ease: 'Power2',
      onComplete: () => {
        flyingHeart.destroy();
      }
    });
  }

  /**
   * Subscribe to score events
   */
  public on(event: 'heartDeducted' , callback: (...args: any[]) => void): void {
    this.events.on(event, callback);
  }

  /**
   * Unsubscribe from score events
   */
  public off(event: 'heartDeducted' , callback: (...args: any[]) => void): void {
    this.events.off(event, callback);
  }

  /**
   * Clean up resources
   */
  public destroy(): void {
    this.events.removeAllListeners();
    
    if (this.container) {
      this.container.destroy();
    }
    
    this.container = undefined;
  }
}
