import{n as b,y as _,ac as y,ad as i,x as v,ae as x,af as m,v as o,ag as C,ah as g,a8 as S,ai as k,aj as w,ak as j,al as q,am as z,an as A,ao as D}from"./UXlYzocm.js";import{a as E,m as M,u as O}from"./AD_Ai2mn.js";import{c as P}from"./DdHBXBi3.js";function U(e,t,n){if(e==null)return t(void 0),b;const s=_(()=>e.subscribe(t,n));return s.unsubscribe?()=>s.unsubscribe():s}const c=[];function K(e,t=b){let n=null;const s=new Set;function r(u){if(y(e,u)&&(e=u,n)){const p=!c.length;for(const a of s)a[1](),c.push(a,e);if(p){for(let a=0;a<c.length;a+=2)c[a][0](c[a+1]);c.length=0}}}function l(u){r(u(e))}function f(u,p=b){const a=[u,p];return s.add(a),s.size===1&&(n=t(r,l)||b),u(e),()=>{s.delete(a),s.size===0&&n&&(n(),n=null)}}return{set:r,update:l,subscribe:f}}function L(e){let t;return U(e,n=>t=n)(),t}function $(){return m===null&&x(),(m.ac??=new AbortController).signal}function h(e){o===null&&i(),S&&o.l!==null?d(o).m.push(e):v(()=>{const t=_(e);if(typeof t=="function")return t})}function R(e){o===null&&i(),h(()=>()=>_(e))}function T(e,t,{bubbles:n=!1,cancelable:s=!1}={}){return new CustomEvent(e,{detail:t,bubbles:n,cancelable:s})}function B(){const e=o;return e===null&&i(),(t,n,s)=>{const r=e.s.$$events?.[t];if(r){const l=C(r)?r.slice():[r],f=T(t,n,s);for(const u of l)u.call(e.x,f);return!f.defaultPrevented}return!0}}function F(e){o===null&&i(),o.l===null&&g(),d(o).b.push(e)}function G(e){o===null&&i(),o.l===null&&g(),d(o).a.push(e)}function d(e){var t=e.l;return t.u??={a:[],b:[],m:[]}}const N=Object.freeze(Object.defineProperty({__proto__:null,afterUpdate:G,beforeUpdate:F,createEventDispatcher:B,createRawSnippet:P,flushSync:k,getAbortSignal:$,getAllContexts:w,getContext:j,hasContext:q,hydrate:E,mount:M,onDestroy:R,onMount:h,setContext:z,settled:A,tick:D,unmount:O,untrack:_},Symbol.toStringTag,{value:"Module"}));export{R as a,N as b,L as g,h as o,U as s,K as w};
