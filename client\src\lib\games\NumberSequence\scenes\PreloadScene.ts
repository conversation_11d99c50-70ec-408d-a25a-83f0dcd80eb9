import Phaser from 'phaser';

export default class PreloadScene extends Phaser.Scene {
  constructor() {
    super('PreloadScene');
  }

  preload(): void {
    const { width, height } = this.cameras.main;

    // Create loading bar
    const bgBar = this.add.rectangle(
        width / 2,
        height / 2,
        width / 2,
        20,
        0x232323
    );

    const progressBar = this.add.rectangle(
        bgBar.x - bgBar.width / 2,
        bgBar.y,
        0,
        bgBar.height,
        0x00ff00
    );
    progressBar.setOrigin(0, 0.5);

    const loadingText = this.add.text(
        width / 2,
        height / 2 - 30,
        'Loading...',
        {
          font: '24px Arial',
          color: '#ffffff'
        }
    ).setOrigin(0.5);

    // Update progress bar as assets are loaded
    this.load.on('progress', (value: number) => {
      progressBar.width = bgBar.width * value;
    });

    // Remove progress bar when complete
    this.load.on('complete', () => {
      progressBar.destroy();
      bgBar.destroy();
      loadingText.destroy();
    });

    // Load countdown images
    this.load.image('countdown-1', '/assets/images/countdown-1.png');
    this.load.image('countdown-2', '/assets/images/countdown-2.png');
    this.load.image('countdown-3', '/assets/images/countdown-3.png');
    this.load.image('countdown-go', '/assets/images/countdown-go.png');

    // Load SVG assets
    this.load.svg('heart', '/assets/images/mdi--heart.svg');
    this.load.svg('heart_outline', '/assets/images/mdi-light--heart.svg');
    this.load.svg('heart_broken', '/assets/images/mdi--heart-broken.svg');


    // Load GameStartScene Assets
    this.load.image('game_name', '/assets-numbers/images/game_name.svg');
    this.load.image('button_bg', '/assets/images/button_bg.svg');
    this.load.image('game_start', '/assets/images/game_start.png');

    // Load GameEndScene Assets
    this.load.image('game_background', '/assets/images/game_bg.png');
    this.load.image('game_over', '/assets/images/game_over.svg');
    this.load.image('back_to_lobby', '/assets/images/back_to_lobby.png');

    // Load Timer Assets
    this.load.image('timer_bg', '/assets/images/timer_bg.svg');
    this.load.image('timer_icon', '/assets/images/timer_icon.png');
    this.load.image('timer_countdown_bg', '/assets/images/timer_countdown_bg.png');

    // Load circle for NumberObject
    this.load.image('circle', '/assets-numbers/images/circle.png');

    // Load sounds with multiple format support for browser compatibility
    // Common sounds from centralized location
    this.load.audio('countdown', [
      '/assets/audio/countdown.ogg',
      '/assets/audio/countdown.mp3'
    ]);
    this.load.audio('click', [
      '/assets/audio/click.ogg',
      '/assets/audio/click.mp3'
    ]);

    // Game-specific sounds
    this.load.audio('go', [
      '/assets/sounds/go.ogg',
      '/assets/sounds/go.mp3'
    ]);
    this.load.audio('collect', [
      '/assets-numbers/sounds/collect.ogg',
      '/assets-numbers/sounds/collect.mp3'
    ]);
    this.load.audio('complete', [
      '/assets-numbers/sounds/complete.ogg',
      '/assets-numbers/sounds/complete.mp3'
    ]);
    this.load.audio('error', [
      '/assets-numbers/sounds/error.ogg',
      '/assets-numbers/sounds/error.mp3'
    ]);
    this.load.audio('timeout', [
      '/assets-numbers/sounds/timeout.ogg',
      '/assets-numbers/sounds/timeout.mp3'
    ]);
  }

  create(): void {
    this.scene.start('GameStartScene');
  }
}
