import"../chunks/DsnmJJEf.js";import"../chunks/BzjXworP.js";import{v as g,w as x,x as c,y as b,z as l,A as k,B as d,C as y,D as w,F as z,G as A,I as B,J as C,K as D,L as E,M as u,N as m,O as F}from"../chunks/UXlYzocm.js";import{s as _}from"../chunks/AD_Ai2mn.js";import{p as v}from"../chunks/ByeBDOvZ.js";function G(a=!1){const e=g,t=e.l.u;if(!t)return;let r=()=>y(e.s);if(a){let o=0,s={};const f=w(()=>{let p=!1;const i=e.s;for(const n in i)i[n]!==s[n]&&(s[n]=i[n],p=!0);return p&&o++,o});r=()=>d(f)}t.b.length&&x(()=>{h(e,r),l(t.b)}),c(()=>{const o=b(()=>t.m.map(k));return()=>{for(const s of o)typeof s=="function"&&s()}}),t.a.length&&c(()=>{h(e,r),l(t.a)})}function h(a,e){if(a.l.s)for(const t of a.l.s)d(t);e()}var I=A("<h1> </h1> <p> </p>",1);function O(a,e){z(e,!1),G();var t=I(),r=B(t),o=u(r,!0);m(r);var s=F(r,2),f=u(s,!0);m(s),C(()=>{_(o,v.status),_(f,v.error?.message)}),D(a,t),E()}export{O as component};
