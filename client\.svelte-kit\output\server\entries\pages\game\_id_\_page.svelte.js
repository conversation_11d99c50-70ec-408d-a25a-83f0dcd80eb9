import { N as current_component, O as attr_style, F as escape_html, M as stringify, J as attr, K as attr_class, P as spread_attributes, E as pop, A as push, G as ensure_array_like, I as head, Q as store_get, R as unsubscribe_stores } from "../../../../chunks/index.js";
import { p as page } from "../../../../chunks/index2.js";
import { w as writable } from "../../../../chunks/exports.js";
import io from "socket.io-client";
import Phaser$1 from "phaser";
function html(value) {
  var html2 = String(value ?? "");
  var open = "<!---->";
  return open + html2 + "<!---->";
}
function onDestroy(fn) {
  var context = (
    /** @type {Component} */
    current_component
  );
  (context.d ??= []).push(fn);
}
class PostMessageHandler {
  listeners = /* @__PURE__ */ new Map();
  constructor() {
    this.setupMessageListener();
  }
  setupMessageListener() {
    if (typeof window !== "undefined") {
      window.addEventListener("message", (event) => {
        const message = event.data;
        this.handleMessage(message);
      });
    }
  }
  handleMessage(message) {
    const listeners = this.listeners.get(message.type) || [];
    listeners.forEach((listener) => listener(message.data));
  }
  // Listen for specific message types
  on(messageType, callback) {
    if (!this.listeners.has(messageType)) {
      this.listeners.set(messageType, []);
    }
    this.listeners.get(messageType).push(callback);
  }
  // Remove listener
  off(messageType, callback) {
    const listeners = this.listeners.get(messageType) || [];
    const index = listeners.indexOf(callback);
    if (index > -1) {
      listeners.splice(index, 1);
    }
  }
  // Send message to parent PWA
  sendToParent(type, data) {
    if (typeof window !== "undefined" && window.parent) {
      const message = { type, data };
      window.parent.postMessage(message, "*");
    }
  }
  // Common message handlers
  onGameInit(callback) {
    this.on("gameInit", callback);
  }
  onPause(callback) {
    this.on("pause", callback);
  }
  onResume(callback) {
    this.on("resume", callback);
  }
  // Common message senders
  sendScoreUpdate(score) {
    this.sendToParent("scoreUpdate", { score });
  }
  sendGameComplete(finalScore, submitScoreId) {
    this.sendToParent("gameComplete", { finalScore, submitScoreId });
  }
  sendGameReady() {
    this.sendToParent("gameReady");
  }
  sendError(error) {
    this.sendToParent("error", { error });
  }
}
new PostMessageHandler();
const initialState = {
  score: 0,
  time: 30,
  totalTime: 30,
  maxLives: 3,
  lives: 3,
  isLoading: true,
  loadingProgress: 0,
  isCountdown: false,
  isPlaying: false,
  isPaused: false,
  gameOver: false,
  opponentScore: null,
  roomId: null,
  authToken: null,
  submitScoreId: null,
  gameId: null
};
const gameState = writable(initialState);
const gameActions = {
  updateLoadingProgress: (progress) => {
    gameState.update((state) => ({ ...state, loadingProgress: progress }));
  },
  updateScore: (score) => {
    gameState.update((state) => ({ ...state, score }));
  },
  updateTime: (time) => {
    gameState.update((state) => ({ ...state, time }));
  },
  updateLives: (lives) => {
    gameState.update((state) => ({ ...state, lives }));
  },
  preloadComplete: () => {
    gameState.update((state) => ({ ...state, isLoading: false }));
  },
  initGame: () => {
    gameState.update((state) => ({
      ...state,
      isCountdown: true,
      isPlaying: false,
      isPaused: false,
      gameOver: false
    }));
  },
  startGame: () => {
    gameState.update((state) => ({
      ...state,
      isPlaying: true
    }));
  },
  pauseGame: () => {
    gameState.update((state) => ({ ...state, isPaused: true }));
  },
  resumeGame: () => {
    gameState.update((state) => ({ ...state, isPaused: false }));
  },
  endGame: () => {
    gameState.update((state) => ({
      ...state,
      isPlaying: false,
      gameOver: true
    }));
  },
  resetGame: () => {
    gameState.set(initialState);
  },
  setOpponentScore: (opponentScore) => {
    gameState.update((state) => ({ ...state, opponentScore }));
  },
  setRoomData: (roomId, authToken, submitScoreId) => {
    gameState.update((state) => ({
      ...state,
      roomId,
      authToken,
      submitScoreId
    }));
  },
  setGameId: (gameId) => {
    gameState.update((state) => ({ ...state, gameId }));
  }
};
const PUBLIC_SOCKET_SERVER_URL = "ws://localhost:3000";
class SocketClient {
  socket = null;
  serverUrl;
  constructor() {
    this.serverUrl = PUBLIC_SOCKET_SERVER_URL;
    console.log(this.serverUrl);
  }
  async connect(authToken, callbacks) {
    return new Promise((resolve, reject) => {
      this.socket = io(this.serverUrl, {
        auth: {
          token: authToken
        }
      });
      this.socket.on("connect", () => {
        console.log("Connected to game server");
        this.setupEventListeners(callbacks);
        resolve();
      });
      this.socket.on("connect_error", (error) => {
        console.error("Connection error:", error);
        reject(error);
      });
    });
  }
  setupEventListeners(callbacks) {
    if (!this.socket) return;
    this.socket.on("gameUpdate", (data) => {
      console.log("Game update received:", data);
    });
    this.socket.on("opponentScore", (score) => {
      gameActions.setOpponentScore(score);
    });
    this.socket.on("roomJoined", (roomData) => {
      console.log("Joined room:", roomData);
    });
    this.socket.on("playerJoined", (playerData) => {
      console.log("Player joined:", playerData);
    });
    this.socket.on("playerLeft", (playerData) => {
      console.log("Player left:", playerData);
    });
    this.socket.on("gameStarted", () => {
      gameActions.startGame();
    });
    this.socket.on("gameEnded", (results) => {
      gameActions.endGame();
      console.log("Game ended:", results);
    });
    this.socket.on("started", (data) => {
      console.log("Game started:", data);
      gameActions.startGame();
    });
    this.socket.on("ended", (data) => {
      console.log("Game ended:", data);
      callbacks.onGameComplete(data.finalScore);
    });
    this.socket.on("action_result", (data) => {
      console.log("Game action result:", data);
      if (data.actionType === "tile_tap") {
        console.log("Tile tap result:", data.data);
        callbacks.onScoreUpdate(data.data.newScore);
      }
    });
    this.socket.on("score", (data) => {
      console.log("Score update from server:", data);
      gameActions.updateScore(data.score);
    });
    this.socket.on("timer_tick", (data) => {
      console.log("Timer update from server:", data);
      gameActions.updateTime(data.duration);
    });
    this.socket.on("error", (data) => {
      console.error("Game error:", data);
    });
  }
  joinRoom(roomId) {
    if (this.socket) {
      this.socket.emit("joinRoom", { roomId });
    }
  }
  createRoom(gameId) {
    if (this.socket) {
      this.socket.emit("createRoom", { gameId });
    }
  }
  sendScore(score, roomId) {
    if (this.socket) {
      this.socket.emit("submit_score", {
        score,
        roomId: roomId || "default-room",
        playerId: "player-1",
        // TODO: Get actual player ID
        gameType: "finger-frenzy"
        // TODO: Get actual game type
      });
    }
  }
  sendGameEvent(eventType, data) {
    if (this.socket) {
      this.socket.emit("game_action", {
        action: eventType,
        gameData: data,
        gameId: data.gameId || "finger-frenzy",
        roomId: data.roomId || "default-room",
        playerId: "player-1"
        // TODO: Get actual player ID
      });
    }
  }
  // Generic game methods
  startGame(gameId, roomId = "default-room") {
    if (this.socket) {
      this.socket.emit("start", {
        gameId,
        roomId
      });
    }
  }
  endGame(gameId, roomId = "default-room", reason = "manual") {
    if (this.socket) {
      this.socket.emit("end", {
        gameId,
        roomId,
        reason
      });
    }
  }
  sendGameAction(gameId, roomId, actionType, actionData) {
    if (this.socket) {
      this.socket.emit("action", {
        gameId,
        roomId,
        action: {
          type: actionType,
          data: actionData
        }
      });
    }
  }
  // Convenience methods for specific actions
  sendTileTap(gameId, roomId, tileId, reactionTime) {
    this.sendGameAction(gameId, roomId, "tile_tap", {
      tileId,
      reactionTime: reactionTime || 0,
      clickTime: Date.now()
    });
  }
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }
  isConnected() {
    return this.socket?.connected ?? false;
  }
  // Add custom event listener
  addCustomEventListener(event, callback) {
    if (this.socket) {
      this.socket.on(event, callback);
    }
  }
  // Remove custom event listener
  removeCustomEventListener(event, callback) {
    if (this.socket) {
      this.socket.off(event, callback);
    }
  }
}
new SocketClient();
let GameConfig$1 = class GameConfig {
  // Game dimensions
  static GAME_WIDTH = 540;
  static GAME_HEIGHT = 960;
  static BACKGROUND_COLOR = "#0E0F1E";
  // Grid configuration
  static GRID_SIZE = 4;
  static INITIAL_ACTIVE_BLOCKS = 3;
  static BLOCK_CORNER_RADIUS = 10;
  // Timer settings
  static GAME_DURATION = 30;
  // in seconds
  static COOLDOWN_DURATION = 0.5;
  // in seconds
  static TMER_INCREASE = 0.2;
  // in seconds
  static TMER_PUNISHMENT = 1;
  // in seconds
  static COUNTDOWN_DURATION = 3;
  // in seconds
  static TRANSITION_DURATION = 300;
  // in milliseconds
  // Scoring configuration
  static SCORE_TIERS = {
    FAST: 5,
    // < 500ms
    MEDIUM_FAST: 4,
    // < 1000ms
    MEDIUM: 3,
    // < 1500ms
    MEDIUM_SLOW: 2,
    // < 2000ms
    SLOW: 1
    // >= 2000ms
  };
  static SCORE_TIER_THRESHOLDS = {
    FAST: 500,
    MEDIUM_FAST: 1e3,
    MEDIUM: 1500,
    MEDIUM_SLOW: 2e3
  };
  static WRONG_CLICK_PENALTY = 5;
  // Animation settings
  static BLOCK_ANIMATION_DURATION = 50;
  static WRONG_EFFECT_DURATION = 200;
  static SCORE_ANIMATION_DURATION = 400;
  static FLASH_DURATION = 100;
  // UI settings
  static TIMER_BAR_WIDTH_PERCENT = 0.8;
  // 80% of screen width
  static TIMER_BAR_HEIGHT = 35;
  static TIMER_BAR_Y_PERCENT = 0.07;
  // 7% from top
  static GRID_CONTAINER_WIDTH_PERCENT = 0.8;
  // 80% of screen width
  static GRID_CONTAINER_HEIGHT_RATIO = 1.4;
  // height = width * 1.4
  static GRID_CONTAINER_Y_PERCENT = 0.63;
  // 63% from top
  static GRID_GAP_PERCENT = 0.03;
  // 3% gap between cells
  // Input settings
  static MAX_ACTIVE_POINTERS = 1;
  // Single touch only to prevent multi-touch conflicts
  // Colors
  static TIMER_GRADIENT_COLORS = ["#33DDFF", "#664DFF"];
  // Cyan to purple
  static SCORE_GRADIENT_COLORS = ["#4cffae", "#32c4ff", "#5c67ff"];
  // Green to blue
  static BUTTON_GRADIENT_COLORS = ["#32c4ff", "#7f54ff", "#b63efc"];
  // Cyan to purple to pink
  static WARNING_COLOR = "#ff0000";
  // Red for warnings/errors
  // Sound settings
  static SOUND_VOLUME = 0.7;
  // Asset keys
  static ASSETS = {
    IMAGES: {
      BLOCK_ACTIVE: "block_active",
      BLOCK_INACTIVE: "block_inactive",
      GAME_START: "game_start",
      GAME_NAME: "game_name",
      TIMER_BG: "timer_bg",
      TIMER_ICON: "timer_icon",
      TIMER_COUNTDOWN_BG: "timer_countdown_bg",
      COUNTDOWN_3: "countdown-3",
      COUNTDOWN_2: "countdown-2",
      COUNTDOWN_1: "countdown-1",
      COUNTDOWN_GO: "countdown-go",
      BUTTON_BG: "button_bg",
      GAME_OVER: "game_over",
      BACK_TO_LOBBY: "back_to_lobby",
      GAME_BACKGROUND: "game_bg"
    },
    SOUNDS: {
      TAP: "tap",
      RIGHT: "right",
      WRONG: "wrong",
      TIMEOUT: "timeout",
      CLICK: "click",
      COUNTDOWN: "countdown",
      GO: "go"
    }
  };
  // Debug settings
  static DEBUG_MODE = true;
  static SHOW_FPS = false;
};
let PreloadScene$3 = class PreloadScene extends Phaser$1.Scene {
  constructor() {
    super({ key: "PreloadScene" });
  }
  preload() {
    this.load.on("progress", (value) => {
      gameActions.updateLoadingProgress(value);
    });
    this.load.on("complete", () => {
      gameActions.preloadComplete();
    });
    this.load.image("block_active", "/assets-finger-frenzy/images/block_active.png");
    this.load.image("block_inactive", "/assets-finger-frenzy/images/block_inactive.png");
    this.load.image("game_name", "/assets-finger-frenzy/images/game_name.png");
    this.load.image("game_start", "/assets/images/game_start.png");
    this.load.image("timer_icon", "/assets/images/timer_icon.png");
    this.load.image("countdown-3", "/assets/images/countdown-3.png");
    this.load.image("countdown-2", "/assets/images/countdown-2.png");
    this.load.image("countdown-1", "/assets/images/countdown-1.png");
    this.load.image("countdown-go", "/assets/images/countdown-go.png");
    this.load.image("back_to_lobby", "/assets/images/back_to_lobby.png");
    this.load.image("game_bg", "/assets/images/game_bg.png");
    this.load.svg("heart", "/assets/images/mdi--heart.svg");
    this.load.svg("heart_outline", "/assets/images/mdi-light--heart.svg");
    this.load.svg("heart_broken", "/assets/images/mdi--heart-broken.svg");
    this.load.svg("button_bg", "/assets/images/button_bg.svg");
    this.load.svg("game_over", "/assets/images/game_over.svg");
    this.load.svg("timer_bg", "/assets/images/timer_bg.svg");
    this.load.image("timer_countdown_bg", "/assets/images/timer_countdown_bg.png");
    this.load.audio("click", ["/assets/audio/click.ogg", "/assets/audio/click.mp3", "/assets/audio/click.wav"]);
    this.load.audio("wrong", ["/assets/audio/wrong.ogg", "/assets/audio/wrong.mp3", "/assets/audio/wrong.wav"]);
    this.load.audio("countdown", ["/assets/audio/countdown.ogg", "/assets/audio/countdown.mp3", "/assets/audio/countdown.wav"]);
    this.load.audio("go", ["/assets/audio/go.mp3", "/assets/audio/go.wav"]);
    this.load.audio("tap", ["/assets-finger-frenzy/sounds/tap.ogg", "/assets-finger-frenzy/sounds/tap.mp3", "/assets-finger-frenzy/sounds/tap.wav"]);
    this.load.audio("right", ["/assets-finger-frenzy/sounds/right.ogg", "/assets-finger-frenzy/sounds/right.mp3", "/assets-finger-frenzy/sounds/right.wav"]);
    this.load.audio("timeout", ["/assets-finger-frenzy/sounds/timeout.ogg", "/assets-finger-frenzy/sounds/timeout.mp3", "/assets-finger-frenzy/sounds/timeout.wav"]);
  }
  create() {
  }
};
let TicTapsConnector$3 = class TicTapsConnector {
  isWebGL;
  constructor() {
    this.isWebGL = this.checkIfWebGL();
  }
  /**
   * Check if running in a browser environment and embedded
   */
  checkIfWebGL() {
    return typeof window !== "undefined" && window.parent && window.parent !== window;
  }
  /**
   * Notify parent window that the game is ready
   * Equivalent to TicTaps.Instance.NotifyGameReady()
  */
  hasNotifiedReady = false;
  notifyGameReady() {
    console.log("notifyGameReady -- ENTER");
    if (this.hasNotifiedReady) return;
    this.hasNotifiedReady = true;
    console.log("Notifying game ready");
    this.sendMessage({ type: "gameReady" });
  }
  /**
   * Send score to parent window
   * Equivalent to TicTaps.Instance.SendScore()
   */
  sendScore(score) {
    this.sendMessage({ type: "gameScore", score });
  }
  /**
   * Notify parent window that the game has quit
   * Equivalent to TicTaps.Instance.NotifyGameQuit()
   */
  notifyGameQuit() {
    this.sendMessage({ type: "gameQuit" });
  }
  /**
  * Send a typed message to the parent window
  */
  sendMessage(message) {
    if (this.isWebGL && window.parent && typeof window.parent.postMessage === "function") {
      window.parent.postMessage(message, "*");
      console.log("Message sent to parent:", message);
    }
  }
};
let GameStartScene$3 = class GameStartScene extends Phaser.Scene {
  ticTaps;
  startButton;
  isStarting = false;
  constructor() {
    super("GameStartScene");
  }
  create() {
    const { width, height } = this.cameras.main;
    this.ticTaps = new TicTapsConnector$3();
    this.add.image(0, 0, "game_bg").setOrigin(0, 0).setDisplaySize(width, height);
    const gameTitle = this.add.image(
      width / 2,
      height * 0.25,
      // Positioned at about 25% from the top
      "game_name"
    ).setOrigin(0.5);
    const titleScale = (
      // (width * 0.6) / gameTitle.width;
      Math.min(width * 0.7 / gameTitle.width, 0.8)
    );
    gameTitle.setScale(titleScale);
    this.tweens.add({
      targets: gameTitle,
      scaleX: titleScale * 1.02,
      scaleY: titleScale * 1.02,
      duration: 1500,
      yoyo: true,
      repeat: -1,
      ease: "Sine.easeInOut"
    });
    this.startButton = this.add.image(
      width / 2,
      height * 0.6,
      // Positioned at about 60% from the top
      "button_bg"
    ).setOrigin(0.5);
    const buttonScale = (
      // (width * 0.6) / this.startButton.width;
      Math.min(width * 0.6 / this.startButton.width, 0.4)
    );
    this.startButton.setScale(buttonScale);
    const startText = this.add.image(
      this.startButton.x,
      this.startButton.y - 5,
      "game_start"
    ).setOrigin(0.5);
    const textScale = this.startButton.displayWidth * 0.6 / startText.width;
    startText.setScale(textScale);
    this.startButton.setInteractive({ useHandCursor: true });
    this.startButton.on("pointerover", () => {
      this.tweens.add({
        targets: [this.startButton, startText],
        scaleX: buttonScale * 1.05,
        scaleY: buttonScale * 1.05,
        duration: 150,
        ease: "Sine.easeOut"
      });
    });
    this.startButton.on("pointerout", () => {
      this.tweens.add({
        targets: [this.startButton, startText],
        scaleX: buttonScale,
        scaleY: buttonScale,
        duration: 150,
        ease: "Sine.easeOut"
      });
    });
    this.startButton.on("pointerdown", () => {
      if (this.sound.get("countdown")) {
        this.sound.play("countdown", { volume: 0.7 });
      }
      this.ticTaps.notifyGameReady();
      this.tweens.add({
        targets: [this.startButton, startText],
        scaleX: buttonScale * 0.95,
        scaleY: buttonScale * 0.95,
        duration: 100,
        yoyo: true,
        onComplete: () => this.startGameCountdown(width, height)
      });
    });
  }
  startGameCountdown(width, height) {
    if (this.isStarting) return;
    this.isStarting = true;
    const flash = this.add.rectangle(
      width / 2,
      height / 2,
      width,
      height,
      16777215
    ).setAlpha(0).setOrigin(0.5);
    flash.setDepth(1e3);
    this.tweens.add({
      targets: flash,
      alpha: 0.8,
      duration: 100,
      ease: "Sine.easeOut",
      onComplete: () => {
        if (this.sound.get("go")) {
          this.sound.play("go", { volume: 0.7 });
        }
        this.tweens.add({
          targets: flash,
          alpha: 0,
          delay: 50,
          // Hold for 50ms
          duration: 250,
          ease: "Sine.easeIn",
          onComplete: () => {
            this.scene.start("GameScene");
          }
        });
      }
    });
  }
};
let ScoreManager$3 = class ScoreManager {
  scene;
  config;
  score;
  // UI Elements
  scoreText;
  scoreLabel;
  container;
  // Events
  events;
  constructor(scene, config = {}) {
    this.scene = scene;
    this.events = new Phaser$1.Events.EventEmitter();
    this.config = {
      initialScore: config.initialScore ?? 0,
      fontFamily: config.fontFamily ?? "Arial",
      fontSize: config.fontSize ?? "80px",
      labelFontSize: config.labelFontSize ?? "28px",
      scoreColor: config.scoreColor ?? "#33DDFF",
      labelColor: config.labelColor ?? "#FFFFFF",
      animationColor: config.animationColor ?? "#ffff00",
      animationDuration: config.animationDuration ?? 800
    };
    this.score = this.config.initialScore;
  }
  /**
   * Create the score UI elements at the specified position
   */
  createUI(x, y, parentContainer) {
    this.container = this.scene.add.container(0, 0);
    this.scoreLabel = this.scene.add.text(x, y - 30, "Total Point", {
      fontFamily: this.config.fontFamily,
      fontSize: this.config.labelFontSize,
      fontStyle: "bold",
      color: this.config.labelColor
    }).setOrigin(0.5);
    this.scoreText = this.scene.add.text(x, y + 30, this.score.toString(), {
      fontFamily: this.config.fontFamily,
      fontSize: this.config.fontSize,
      fontStyle: "bold",
      color: this.config.scoreColor
    }).setOrigin(0.5);
    this.container.add([this.scoreLabel, this.scoreText]);
    if (parentContainer) {
      parentContainer.add(this.container);
    }
  }
  /**
   * Add points to the score with optional animation
   */
  addPoints(points, animationConfig) {
    this.score += points;
    this.updateScoreDisplay();
    if (animationConfig) {
      this.createScoreAnimation(animationConfig);
    }
    gameActions.updateScore(this.score);
    this.events.emit("scoreChanged", this.score, points);
  }
  /**
   * Subtract points from the score with optional animation
   */
  subtractPoints(points, animationConfig) {
    this.score = Math.max(0, this.score - points);
    this.updateScoreDisplay();
    if (animationConfig) {
      this.createScoreAnimation(animationConfig);
    }
    gameActions.updateScore(this.score);
    this.events.emit("scoreChanged", this.score, -points);
  }
  /**
   * Set the score to a specific value
   */
  setScore(newScore) {
    const oldScore = this.score;
    this.score = newScore;
    this.updateScoreDisplay();
    this.events.emit("scoreChanged", this.score, this.score - oldScore);
  }
  /**
   * Get the current score
   */
  getScore() {
    return this.score;
  }
  /**
   * Reset score to initial value
   */
  reset() {
    this.score = this.config.initialScore;
    this.updateScoreDisplay();
    this.events.emit("scoreReset", this.score);
  }
  /**
   * Update the score display text
   */
  updateScoreDisplay() {
    if (this.scoreText) {
      this.scoreText.setText(this.score.toString());
    }
  }
  /**
   * Create animated flying score text
   */
  createScoreAnimation(config) {
    const animationText = this.scene.add.text(
      config.startX,
      config.startY,
      `+${config.points}`,
      {
        fontFamily: this.config.fontFamily,
        fontSize: "24px",
        color: config.color ?? this.config.animationColor,
        stroke: "#000000",
        strokeThickness: 3
      }
    );
    animationText.setOrigin(0.5);
    this.scene.tweens.add({
      targets: animationText,
      y: config.startY - 50,
      alpha: 0,
      scale: 1.2,
      duration: config.duration ?? this.config.animationDuration,
      ease: "Power2",
      onComplete: () => {
        animationText.destroy();
      }
    });
  }
  /**
   * Subscribe to score events
   */
  on(event, callback) {
    this.events.on(event, callback);
  }
  /**
   * Unsubscribe from score events
   */
  off(event, callback) {
    this.events.off(event, callback);
  }
  /**
   * Clean up resources
   */
  destroy() {
    this.events.removeAllListeners();
    if (this.container) {
      this.container.destroy();
    }
    this.scoreText = void 0;
    this.scoreLabel = void 0;
    this.container = void 0;
  }
};
class GridBlock extends Phaser$1.GameObjects.Container {
  tileId;
  blockActiveImage;
  blockInactiveImage;
  isBlockActive = false;
  activationTime = 0;
  row;
  col;
  isShowingWrongState = false;
  constructor(scene, x, y, width, height, row, col) {
    super(scene, x, y);
    this.tileId = `block_${row}_${col}`;
    this.row = row;
    this.col = col;
    this.blockInactiveImage = scene.add.image(0, 0, GameConfig$1.ASSETS.IMAGES.BLOCK_INACTIVE);
    this.blockInactiveImage.setDisplaySize(width, height);
    this.blockInactiveImage.setOrigin(0.5, 0.5);
    this.add(this.blockInactiveImage);
    this.blockActiveImage = scene.add.image(0, 0, GameConfig$1.ASSETS.IMAGES.BLOCK_ACTIVE);
    this.blockActiveImage.setDisplaySize(width * 0.6, height * 0.85);
    this.blockActiveImage.setOrigin(0.5, 0.5);
    this.blockActiveImage.setVisible(false);
    this.add(this.blockActiveImage);
    scene.add.existing(this);
    this.setSize(width, height);
    this.setInteractive({ useHandCursor: true });
  }
  getBlockActive() {
    return this.isBlockActive;
  }
  getActivationTime() {
    return this.activationTime;
  }
  getTileId() {
    return this.tileId;
  }
  setBlockActive(active) {
    if (this.isBlockActive === active) return;
    this.isBlockActive = active;
    this.blockActiveImage.setVisible(active);
    this.blockInactiveImage.setVisible(!active);
    if (active) {
      this.activationTime = Date.now();
      this.blockActiveImage.setScale(0.95);
      this.scene.tweens.add({
        targets: this.blockActiveImage,
        scale: 1,
        duration: GameConfig$1.BLOCK_ANIMATION_DURATION,
        ease: "Back.easeOut"
      });
    }
  }
  setBlockWrong() {
    if (this.isShowingWrongState) return;
    this.isShowingWrongState = true;
    const image = this.isBlockActive ? this.blockActiveImage : this.blockInactiveImage;
    image.setTint(16729156);
    this.scene.time.delayedCall(400, () => {
      image.setTint(16777215);
      this.isShowingWrongState = false;
    });
  }
  reset() {
    this.isBlockActive = false;
    this.isShowingWrongState = false;
    this.blockActiveImage.setVisible(false);
    this.blockActiveImage.setTint(16777215);
    this.blockActiveImage.setScale(1);
    this.blockInactiveImage.setVisible(true);
    this.blockInactiveImage.setTint(16777215);
  }
}
let GameScene$3 = class GameScene extends Phaser$1.Scene {
  blocks = [];
  gameEnd = false;
  // UI Elements
  gridContainer;
  scoreManager;
  // TicTaps integration
  ticTaps;
  socketClient = null;
  // Game session data
  roomId = "room-" + Date.now().toString(36);
  gameId = "finger-frenzy";
  constructor() {
    super({ key: "GameScene" });
    this.ticTaps = new TicTapsConnector$3();
  }
  init() {
    this.gameEnd = false;
    const gameConfig = this.registry.get("gameConfig");
    this.socketClient = gameConfig?.socketClient || null;
    this.roomId = gameConfig?.roomId || "room-" + Date.now().toString(36);
    this.gameId = gameConfig?.gameId || "finger-frenzy";
    this.setupSocketEventListeners();
    this.scoreManager = new ScoreManager$3(this, {
      initialScore: 0,
      fontSize: "80px",
      scoreColor: "#33DDFF"
    });
  }
  create() {
    this.createBackground();
    this.createGrid();
    gameActions.startGame();
    this.startCountdown();
  }
  shutdown() {
    this.tweens.killAll();
    this.cleanupSocketEventListeners();
    if (this.scoreManager) {
      this.scoreManager.destroy();
    }
  }
  /**
   * Clean up socket event listeners
   */
  cleanupSocketEventListeners() {
    if (!this.socketClient) return;
    this.socketClient.removeCustomEventListener("started", () => {
    });
    this.socketClient.removeCustomEventListener("action_result", () => {
    });
    this.socketClient.removeCustomEventListener("game_error", () => {
    });
    console.log("Socket event listeners cleaned up for FingerFrenzy GameScene");
  }
  /**
   * Setup socket event listeners for server communication
   */
  setupSocketEventListeners() {
    if (!this.socketClient) return;
    this.socketClient.addCustomEventListener("started", (data) => {
      console.log("Game started by server:", data);
      this.startGame();
    });
    this.socketClient.addCustomEventListener("action_result", (data) => {
      console.log("Action result from server:", data);
      if (data.actionType === "tile_tap") {
        this.handleTileTapResult(data.data);
      }
    });
    this.socketClient.addCustomEventListener("game_error", (data) => {
      console.error("Game error from server:", data);
    });
    console.log("Socket event listeners setup for FingerFrenzy GameScene");
  }
  createBackground() {
    const { width, height } = this.cameras.main;
    const bgTexture = this.textures.createCanvas("bgTexture", width, height);
    const bgContext = bgTexture?.getContext();
    if (bgContext && bgTexture) {
      const gradient = bgContext.createLinearGradient(0, 0, 0, height);
      gradient.addColorStop(0, "#212429");
      gradient.addColorStop(1, "#1C1D22");
      bgContext.fillStyle = gradient;
      bgContext.fillRect(0, 0, width, height);
      bgTexture.refresh();
      this.add.image(width / 2, height / 2, "bgTexture").setOrigin(0.5);
    } else {
      this.cameras.main.setBackgroundColor("#1C1D22");
    }
  }
  async startCountdown() {
    for (let i = 0; i < 4; i++) {
      try {
        this.sound.play(i === 3 ? "go" : "countdown");
      } catch (error) {
        console.warn("Sound playback failed:", error);
      }
      await new Promise((resolve) => {
        this.time.delayedCall(1300, () => resolve());
      });
    }
    if (this.socketClient && this.socketClient.isConnected()) {
      this.socketClient.startGame(this.gameId, this.roomId);
    }
  }
  createGrid() {
    const { width, height } = this.cameras.main;
    const containerWidth = Math.min(width * 0.9, 550);
    const containerHeight = containerWidth * 1.2;
    this.gridContainer = this.add.container(width / 2, height * 0.6);
    const bg = this.add.graphics();
    bg.fillStyle(1710618, 1);
    bg.fillRoundedRect(-containerWidth / 2, -containerHeight / 2, containerWidth, containerHeight, 20);
    bg.lineStyle(4, 4553205, 1);
    bg.strokeRoundedRect(-containerWidth / 2, -containerHeight / 2, containerWidth, containerHeight, 20);
    this.gridContainer.add(bg);
    this.blocks = [];
    const gap = containerWidth * 0.03;
    const cellWidth = (containerWidth - gap * 5) / 4;
    const cellHeight = (containerHeight - gap * 5) / 4;
    const startX = -containerWidth / 2 + gap;
    const startY = -containerHeight / 2 + gap;
    for (let row = 0; row < 4; row++) {
      for (let col = 0; col < 4; col++) {
        const x = startX + col * (cellWidth + gap) + cellWidth / 2;
        const y = startY + row * (cellHeight + gap) + cellHeight / 2;
        const block = new GridBlock(this, x, y, cellWidth, cellHeight, row, col);
        this.blocks.push(block);
        this.gridContainer.add(block);
        block.on("pointerdown", (pointer) => this.onBlockClick(block, pointer));
      }
    }
  }
  onBlockClick(block, pointer) {
    if (this.gameEnd || !block) return;
    if (pointer.getDuration() > 100) {
      return;
    }
    block.disableInteractive();
    const currentTime = Date.now();
    const reactionTime = currentTime - block.getActivationTime();
    console.log("reactionTime", reactionTime);
    if (this.socketClient && this.socketClient.isConnected()) {
      const tileId = this.getTileId(block);
      this.socketClient.sendTileTap(this.gameId, this.roomId, tileId, reactionTime);
    }
  }
  activateRandomBlock(excludeBlockIndex) {
    if (this.gameEnd) return;
    const currentActiveCount = this.blocks.filter((b) => b.getBlockActive()).length;
    if (currentActiveCount >= GameConfig$1.INITIAL_ACTIVE_BLOCKS) return;
    const availablePositions = [];
    this.blocks.forEach((block, index) => {
      const isActive = block.getBlockActive();
      const isExcluded = excludeBlockIndex !== void 0 && index === excludeBlockIndex;
      if (!isActive && !isExcluded) {
        availablePositions.push(index);
      }
    });
    if (availablePositions.length > 0) {
      const randomIndex = Phaser$1.Utils.Array.GetRandom(availablePositions);
      this.blocks[randomIndex].setBlockActive(true);
    }
  }
  startGame() {
    this.blocks.forEach((block) => block.reset());
    const positions = [
      { row: 1, col: 2 },
      { row: 2, col: 3 },
      { row: 0, col: 1 }
    ];
    for (const pos of positions) {
      const index = pos.row * 4 + pos.col;
      if (index < this.blocks.length) {
        this.blocks[index].setBlockActive(true);
      }
    }
    console.log("Game started with 3 active blocks");
  }
  endGame() {
    if (this.gameEnd) return;
    this.gameEnd = true;
    gameActions.endGame();
    this.sound.play("timeout");
    this.blocks.forEach((block) => block.disableInteractive());
  }
  quitGame() {
    this.endGame();
    if (this.socketClient && this.socketClient.isConnected()) {
      this.socketClient.endGame(this.gameId, this.roomId, "manual");
    }
  }
  /**
   * Get tile ID for a block based on its position in the grid
   */
  getTileId(block) {
    const blockIndex = this.blocks.indexOf(block);
    const row = Math.floor(blockIndex / 4);
    const col = blockIndex % 4;
    return `block_${row}_${col}`;
  }
  /**
   * Handle tile tap result from server
   */
  handleTileTapResult(data) {
    console.log("Handling tile tap result:", data);
    const block = this.blocks.find((b) => b.getTileId() === data.tileId);
    if (!block) {
      console.warn("Block not found for tile ID:", data.tileId);
      return;
    }
    console.log("Block found:", block.getTileId());
    if (data.isCorrect) {
      this.sound.play("right");
      block.setBlockActive(false);
      this.scoreManager.addPoints(data.points, {
        startX: this.gridContainer.x + block.x,
        startY: this.gridContainer.y + block.y,
        color: "#ffff00",
        points: data.points
      });
    } else {
      this.sound.play("wrong");
      block.setBlockWrong();
      gameActions.updateLives(data.newLives);
      this.scoreManager.subtractPoints(data.points, {
        startX: this.gridContainer.x + block.x,
        startY: this.gridContainer.y + block.y,
        color: "#ff0000",
        points: data.points
      });
    }
    this.time.delayedCall(GameConfig$1.COOLDOWN_DURATION * 1e3, () => {
      block.setInteractive({ useHandCursor: true });
    });
    if (data.gameState) {
      gameActions.updateScore(data.gameState.score);
    }
    if (data.gridState) {
      this.syncGridState(data.gridState);
    }
    if (data.gameEnded) {
      this.endGame();
    }
  }
  /**
   * Sync grid state with server
   */
  syncGridState(gridState) {
    if (!gridState || !gridState.blocks) return;
    gridState.blocks.forEach((serverBlock) => {
      const blockIndex = serverBlock.index;
      if (blockIndex >= 0 && blockIndex < this.blocks.length) {
        this.blocks[blockIndex].setBlockActive(serverBlock.isActive);
      }
    });
  }
};
let GameEndScene$3 = class GameEndScene extends Phaser$1.Scene {
  ticTaps;
  score = 0;
  backToLobbyButton;
  constructor() {
    super("GameEndScene");
    this.ticTaps = new TicTapsConnector$3();
  }
  init(data) {
    this.score = data.score || 0;
    console.log("GameEndScene init - Score:", this.score);
  }
  create() {
    this.ticTaps = new TicTapsConnector$3();
    this.add.image(0, 0, "game_bg").setOrigin(0, 0).setDisplaySize(this.cameras.main.width, this.cameras.main.height);
    const panelWidth = this.cameras.main.width * 0.8;
    const panelHeight = this.cameras.main.height * 0.6;
    if (this.sys.game.renderer.type === Phaser$1.WEBGL) {
      const blurBg = this.add.graphics();
      blurBg.fillStyle(0, 0.3);
      blurBg.fillRoundedRect(
        this.cameras.main.width / 2 - panelWidth / 2 - 2,
        this.cameras.main.height / 2 - panelHeight / 2 - 2,
        panelWidth + 4,
        panelHeight + 4,
        20
      );
      blurBg.postFX.addBlur(0, 0, 1, 2, 1, 1);
    }
    const panel = this.add.graphics();
    panel.fillStyle(1712945, 0.4);
    panel.fillRoundedRect(
      this.cameras.main.width / 2 - panelWidth / 2,
      this.cameras.main.height / 2 - panelHeight / 2,
      panelWidth,
      panelHeight,
      20
    );
    const gameOverImage = this.add.image(
      this.cameras.main.width / 2,
      this.cameras.main.height / 2 - panelHeight * 0.5,
      // Positioned at top of panel
      "game_over"
    ).setOrigin(0.5);
    const gameOverScale = panelWidth * 0.8 / gameOverImage.width;
    gameOverImage.setScale(gameOverScale);
    if (this.sys.game.renderer.type === Phaser$1.WEBGL) {
      gameOverImage.postFX.addGlow(4980654, 1, 0, false, 0.1, 15);
    }
    this.add.text(
      this.cameras.main.width / 2,
      this.cameras.main.height / 2 - 100,
      // Slight offset above center
      "SCORE",
      {
        fontFamily: "Arial",
        fontSize: "30px",
        fontStyle: "bold",
        color: "#FFFFFF"
      }
    ).setOrigin(0.5);
    this.createGradientText(
      this.cameras.main.width / 2,
      this.cameras.main.height / 2,
      // Below "SCORE" text
      this.score.toString(),
      90,
      true
      // Make it with gradient and white outline
    );
    this.createBackToLobbyButton();
  }
  createBackToLobbyButton() {
    const buttonWidth = this.cameras.main.width * 0.7;
    const buttonHeight = 80;
    const buttonX = this.cameras.main.width / 2;
    const buttonY = this.cameras.main.height / 2 + this.cameras.main.height * 0.2;
    this.backToLobbyButton = this.add.container(buttonX, buttonY);
    const borderCanvas = this.textures.createCanvas("buttonBorder", buttonWidth + 4, buttonHeight + 4);
    if (borderCanvas) {
      const borderContext = borderCanvas.getContext();
      const gradient = borderContext.createLinearGradient(0, 0, buttonWidth + 4, 0);
      gradient.addColorStop(0, "#32c4ff");
      gradient.addColorStop(0.5, "#7f54ff");
      gradient.addColorStop(1, "#b63efc");
      borderContext.strokeStyle = gradient;
      borderContext.lineWidth = 2.5;
      roundRect$3(borderContext, 2, 2, buttonWidth, buttonHeight, 18);
      borderCanvas.refresh();
      const border = this.add.image(0, 0, "buttonBorder").setOrigin(0.5);
      this.backToLobbyButton.add(border);
    }
    const buttonBg = this.add.graphics();
    buttonBg.fillStyle(1185311, 1);
    buttonBg.fillRoundedRect(-buttonWidth / 2 + 2, -buttonHeight / 2 + 2, buttonWidth - 4, buttonHeight - 4, 16);
    this.backToLobbyButton.add(buttonBg);
    if (this.textures.exists("back_to_lobby")) {
      const buttonText = this.add.image(0, 0, "back_to_lobby").setOrigin(0.5);
      const textScale = Math.min(buttonWidth * 0.7 / buttonText.width, buttonHeight * 0.6 / buttonText.height);
      buttonText.setScale(textScale);
      this.backToLobbyButton.add(buttonText);
    } else {
      const buttonText = this.createGradientText(0, 0, "BACK TO LOBBY", 28, false, true);
      this.backToLobbyButton.add(buttonText);
    }
    const hitArea = new Phaser$1.Geom.Rectangle(-buttonWidth / 2, -buttonHeight / 2, buttonWidth, buttonHeight);
    this.backToLobbyButton.setInteractive(hitArea, Phaser$1.Geom.Rectangle.Contains);
    this.backToLobbyButton.on("pointerover", () => {
      this.backToLobbyButton.setScale(1.05);
    });
    this.backToLobbyButton.on("pointerout", () => {
      this.backToLobbyButton.setScale(1);
    });
    this.backToLobbyButton.on("pointerdown", () => {
      if (this.sound.get("laser")) {
        this.sound.play("laser", { volume: 0.7 });
      } else if (this.sound.get("countdown")) {
        this.sound.play("countdown", { volume: 0.7 });
      }
      this.backToLobbyButton.setScale(0.95);
      this.time.delayedCall(100, () => {
        this.backToLobbyButton.setScale(1);
        this.endGame();
      });
    });
  }
  createGradientText(x, y, text, fontSize = 32, isScoreText = false, isButtonText = false) {
    const textureName = "gradientText-" + text.replace(/\s+/g, "-") + "-" + fontSize + (isScoreText ? "-score" : "") + (isButtonText ? "-button" : "");
    if (this.textures.exists(textureName)) {
      this.textures.remove(textureName);
    }
    const width = Math.max(400, text.length * fontSize * 0.7);
    const height = fontSize * 1.5;
    const textCanvas = this.textures.createCanvas(textureName, width, height);
    if (!textCanvas) {
      console.error("Failed to create gradient text canvas");
      return this.add.image(x, y, "").setOrigin(0.5);
    }
    const context = textCanvas.getContext();
    const gradient = context.createLinearGradient(0, 0, width, height * 0.5);
    if (isScoreText) {
      gradient.addColorStop(0, "#4cffae");
      gradient.addColorStop(0.4, "#32c4ff");
      gradient.addColorStop(1, "#5c67ff");
    } else if (isButtonText) {
      gradient.addColorStop(0, "#32c4ff");
      gradient.addColorStop(0.5, "#7f54ff");
      gradient.addColorStop(1, "#b63efc");
    } else {
      gradient.addColorStop(0, "#33DDFF");
      gradient.addColorStop(1, "#664DFF");
    }
    context.font = `bold ${fontSize}px Arial`;
    context.textAlign = "center";
    context.textBaseline = "middle";
    if (isScoreText) {
      context.strokeStyle = "rgba(255, 255, 255, 0.9)";
      context.lineWidth = 5;
      context.strokeText(text, width / 2, height / 2);
    }
    context.fillStyle = gradient;
    context.fillText(text, width / 2, height / 2);
    textCanvas.refresh();
    return this.add.image(x, y, textureName).setOrigin(0.5);
  }
  endGame() {
    const flash = this.add.rectangle(
      this.cameras.main.width / 2,
      this.cameras.main.height / 2,
      this.cameras.main.width,
      this.cameras.main.height,
      16777215
    ).setAlpha(0).setOrigin(0.5);
    flash.setDepth(1e3);
    this.ticTaps.sendScore(this.score);
    this.ticTaps.notifyGameQuit();
    this.tweens.add({
      targets: flash,
      alpha: 0.8,
      duration: 100,
      ease: "Sine.easeOut",
      onComplete: () => {
        this.tweens.add({
          targets: flash,
          alpha: 0,
          delay: 50,
          duration: 250,
          ease: "Sine.easeIn",
          onComplete: () => {
            this.scene.start("GameStartScene");
          }
        });
      }
    });
  }
};
function roundRect$3(ctx, x, y, width, height, radius, fill, stroke) {
  ctx.beginPath();
  ctx.moveTo(x + radius, y);
  ctx.lineTo(x + width - radius, y);
  ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
  ctx.lineTo(x + width, y + height - radius);
  ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
  ctx.lineTo(x + radius, y + height);
  ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
  ctx.lineTo(x, y + radius);
  ctx.quadraticCurveTo(x, y, x + radius, y);
  ctx.closePath();
  {
    ctx.stroke();
  }
}
let PreloadScene$2 = class PreloadScene2 extends Phaser$1.Scene {
  constructor() {
    super("PreloadScene");
  }
  preload() {
    const { width, height } = this.cameras.main;
    const bgBar = this.add.rectangle(
      width / 2,
      height / 2,
      width / 2,
      20,
      2302755
    );
    const progressBar = this.add.rectangle(
      bgBar.x - bgBar.width / 2,
      bgBar.y,
      0,
      bgBar.height,
      65280
    );
    progressBar.setOrigin(0, 0.5);
    const loadingText = this.add.text(
      width / 2,
      height / 2 - 30,
      "Loading...",
      {
        font: "24px Arial",
        color: "#ffffff"
      }
    ).setOrigin(0.5);
    this.load.on("progress", (value) => {
      progressBar.width = bgBar.width * value;
    });
    this.load.on("complete", () => {
      progressBar.destroy();
      bgBar.destroy();
      loadingText.destroy();
    });
    this.load.image("game_background", "/assets/images/game_bg.png");
    this.load.image("countdown-3", "/assets/images/countdown-3.png");
    this.load.image("countdown-2", "/assets/images/countdown-2.png");
    this.load.image("countdown-1", "/assets/images/countdown-1.png");
    this.load.image("countdown-go", "/assets/images/countdown-go.png");
    this.load.svg("heart", "/assets/images/mdi--heart.svg");
    this.load.svg("heart_outline", "/assets/images/mdi-light--heart.svg");
    this.load.svg("heart_broken", "/assets/images/mdi--heart-broken.svg");
    this.load.image("game_name", "/assets-bingo/images/game_name.svg");
    this.load.image("button_bg", "/assets/images/button_bg.svg");
    this.load.image("game_start", "/assets/images/game_start.png");
    this.load.image("timer_icon", "/assets/images/timer_icon.png");
    this.load.image("game_over", "/assets/images/game_over.svg");
    this.load.image("back_to_lobby", "/assets/images/back_to_lobby.png");
    this.load.audio("click", ["/assets/audio/click.mp3", "/assets/audio/click.ogg"]);
    this.load.audio("wrong", ["/assets/audio/wrong.ogg", "/assets/audio/wrong.mp3", "/assets/audio/wrong.wav"]);
    this.load.audio("countdown", ["/assets/audio/countdown.mp3", "/assets/audio/countdown.ogg"]);
    this.load.audio("go", ["/assets/audio/go.mp3", "/assets/audio/go.wav"]);
    this.load.audio("match", ["/assets-bingo/audio/match.mp3", "/assets-bingo/audio/match.ogg"]);
    this.load.audio("win", ["/assets-bingo/audio/win.mp3", "/assets-bingo/audio/win.ogg"]);
    this.load.audio("number-appear", ["/assets-bingo/audio/number_appear.mp3", "/assets-bingo/audio/number_appear.ogg"]);
  }
  create() {
    this.scene.start("GameStartScene");
  }
};
let TicTapsConnector$2 = class TicTapsConnector2 {
  isWebGL;
  constructor() {
    this.isWebGL = this.checkIfWebGL();
  }
  /**
   * Check if running in a browser environment and embedded
   */
  checkIfWebGL() {
    return typeof window !== "undefined" && window.parent && window.parent !== window;
  }
  /**
   * Notify parent window that the game is ready
   * Equivalent to TicTaps.Instance.NotifyGameReady()
   */
  notifyGameReady() {
    this.sendMessage({ type: "gameReady" });
  }
  /**
   * Send score to parent window
   * Equivalent to TicTaps.Instance.SendScore()
   */
  sendScore(score) {
    this.sendMessage({ type: "gameScore", score });
  }
  /**
   * Notify parent window that the game has quit
   * Equivalent to TicTaps.Instance.NotifyGameQuit()
   */
  notifyGameQuit() {
    this.sendMessage({ type: "gameQuit" });
  }
  /**
  * Send a typed message to the parent window
  */
  sendMessage(message) {
    if (this.isWebGL && window.parent && typeof window.parent.postMessage === "function") {
      window.parent.postMessage(message, "*");
      console.log("Message sent to parent:", message);
    }
  }
};
let GameStartScene$2 = class GameStartScene2 extends Phaser.Scene {
  ticTaps;
  startButton;
  isStarting = false;
  constructor() {
    super("GameStartScene");
  }
  create() {
    const { width, height } = this.cameras.main;
    this.ticTaps = new TicTapsConnector$2();
    this.add.image(0, 0, "game_background").setOrigin(0, 0).setDisplaySize(width, height);
    const gameTitle = this.add.image(
      width / 2,
      height * 0.25,
      // Positioned at about 25% from the top
      "game_name"
    ).setOrigin(0.5);
    const titleScale = (
      // (width * 0.6) / gameTitle.width;
      Math.min(width * 0.7 / gameTitle.width, 0.8)
    );
    gameTitle.setScale(titleScale);
    this.tweens.add({
      targets: gameTitle,
      scaleX: titleScale * 1.02,
      scaleY: titleScale * 1.02,
      duration: 1500,
      yoyo: true,
      repeat: -1,
      ease: "Sine.easeInOut"
    });
    this.startButton = this.add.image(
      width / 2,
      height * 0.6,
      // Positioned at about 60% from the top
      "button_bg"
    ).setOrigin(0.5);
    const buttonScale = (
      // (width * 0.6) / this.startButton.width;
      Math.min(width * 0.6 / this.startButton.width, 0.4)
    );
    this.startButton.setScale(buttonScale);
    const startText = this.add.image(
      this.startButton.x,
      this.startButton.y - 5,
      "game_start"
    ).setOrigin(0.5);
    const textScale = this.startButton.displayWidth * 0.6 / startText.width;
    startText.setScale(textScale);
    this.startButton.setInteractive({ useHandCursor: true });
    this.startButton.on("pointerover", () => {
      this.tweens.add({
        targets: [this.startButton, startText],
        scaleX: buttonScale * 1.05,
        scaleY: buttonScale * 1.05,
        duration: 150,
        ease: "Sine.easeOut"
      });
    });
    this.startButton.on("pointerout", () => {
      this.tweens.add({
        targets: [this.startButton, startText],
        scaleX: buttonScale,
        scaleY: buttonScale,
        duration: 150,
        ease: "Sine.easeOut"
      });
    });
    this.startButton.on("pointerdown", () => {
      if (this.sound.get("countdown")) {
        this.sound.play("countdown", { volume: 0.7 });
      }
      this.ticTaps.notifyGameReady();
      this.tweens.add({
        targets: [this.startButton, startText],
        scaleX: buttonScale * 0.95,
        scaleY: buttonScale * 0.95,
        duration: 100,
        yoyo: true,
        onComplete: () => this.startGameCountdown(width, height)
      });
    });
  }
  startGameCountdown(width, height) {
    if (this.isStarting) return;
    this.isStarting = true;
    const flash = this.add.rectangle(
      width / 2,
      height / 2,
      width,
      height,
      16777215
    ).setAlpha(0).setOrigin(0.5);
    flash.setDepth(1e3);
    this.tweens.add({
      targets: flash,
      alpha: 0.8,
      duration: 100,
      ease: "Sine.easeOut",
      onComplete: () => {
        if (this.sound.get("go")) {
          this.sound.play("go", { volume: 0.7 });
        }
        this.tweens.add({
          targets: flash,
          alpha: 0,
          delay: 50,
          // Hold for 50ms
          duration: 250,
          ease: "Sine.easeIn",
          onComplete: () => {
            this.scene.start("GameScene");
          }
        });
      }
    });
  }
};
class BingoCell extends Phaser$1.GameObjects.Container {
  letter;
  number;
  marked;
  isFree;
  blinkTween;
  background;
  markedBg;
  numberText;
  letterText;
  isBingoHeader;
  constructor(scene, x, y, letter, number, isFree = false) {
    super(scene, x, y);
    this.scene = scene;
    this.letter = letter;
    this.number = number;
    this.isFree = isFree;
    this.name = isFree ? "FREE" : `${letter}${number}`;
    this.marked = isFree;
    this.blinkTween = null;
    this.createVisuals();
    if (!isFree) {
      this.setupInteractivity();
    }
    scene.add.existing(this);
  }
  createVisuals() {
    const cellSize = 80;
    const bgGraphics = this.scene.add.graphics();
    bgGraphics.fillStyle(1118498, 1);
    bgGraphics.lineStyle(3, 58798, 1);
    bgGraphics.fillRoundedRect(-cellSize / 2, -cellSize / 2, cellSize, cellSize, 16);
    bgGraphics.strokeRoundedRect(-cellSize / 2, -cellSize / 2, cellSize, cellSize, 16);
    this.add(bgGraphics);
    this.background = bgGraphics;
    const markedGraphics = this.scene.add.graphics();
    markedGraphics.fillGradientStyle(
      3172095,
      // Top-left: blue
      4674303,
      // Top-right: blue-purple
      6180351,
      // Bottom-right: more purple
      2187007,
      // Bottom-left: blue
      1
    );
    markedGraphics.fillRoundedRect(-cellSize / 2, -cellSize / 2, cellSize, cellSize, 16);
    markedGraphics.alpha = this.isFree ? 1 : 0;
    this.add(markedGraphics);
    this.markedBg = markedGraphics;
    const displayText = this.isFree ? "FREE" : this.number.toString();
    const fontSize = this.isFree ? "24px" : "36px";
    this.numberText = this.scene.add.text(
      0,
      0,
      displayText,
      {
        fontFamily: '"TT Neoris", Arial, sans-serif',
        fontSize,
        color: "#FFFFFF",
        align: "center",
        fontStyle: "bold",
        stroke: "#000000",
        strokeThickness: 2,
        shadow: { offsetX: 1, offsetY: 1, color: "#000000", blur: 2, fill: true }
      }
    ).setOrigin(0.5);
    this.add(this.numberText);
    this.letterText = this.scene.add.text(
      -28,
      -28,
      this.letter,
      {
        fontFamily: '"TT Neoris", Arial, sans-serif',
        fontSize: "16px",
        color: "#FFFFFF",
        align: "center",
        fontStyle: "bold"
      }
    ).setOrigin(0.5);
    this.add(this.letterText);
    if (!this.isBingoHeader) {
      this.letterText.alpha = 0;
    }
  }
  setupInteractivity() {
    const cellSize = 80;
    const hitArea = new Phaser$1.Geom.Rectangle(-cellSize / 2, -cellSize / 2, cellSize, cellSize);
    this.setInteractive(hitArea, Phaser$1.Geom.Rectangle.Contains);
    this.on("pointerdown", () => {
      const gameScene = this.scene;
      if (!this.marked && !gameScene.gameEnd) {
        gameScene.checkForMatch(this);
      }
    });
    this.on("pointerover", () => {
      const gameScene = this.scene;
      if (!this.marked && !gameScene.gameEnd) {
        this.background.alpha = 0.8;
        this.scene.game.canvas.style.cursor = "pointer";
      }
    });
    this.on("pointerout", () => {
      const gameScene = this.scene;
      if (!this.marked && !gameScene.gameEnd) {
        this.background.alpha = 1;
        this.scene.game.canvas.style.cursor = "default";
      }
    });
  }
  /**
   * Blink the number to indicate a match
   * Equivalent to blink() in Unity's TableButtonController
   */
  blink() {
    if (this.blinkTween) {
      this.blinkTween.stop();
    }
    this.blinkTween = this.scene.tweens.add({
      targets: this.markedBg,
      alpha: { from: 0, to: 0.5 },
      duration: 240,
      yoyo: true,
      repeat: 3,
      onComplete: () => {
        this.markedBg.alpha = 0;
        this.blinkTween = null;
      }
    });
  }
  /**
   * Mark the cell as clicked
   * Equivalent to PlayPickUpStar() in Unity's TableButtonController
   */
  mark() {
    if (this.marked) return;
    this.marked = true;
    this.scene.tweens.add({
      targets: this.markedBg,
      alpha: 1,
      duration: 200,
      ease: "Power2"
    });
    this.scene.tweens.add({
      targets: this,
      scale: { from: 1.1, to: 1 },
      duration: 300,
      ease: "Back.Out"
    });
    if (this.blinkTween) {
      this.blinkTween.stop();
      this.blinkTween = null;
    }
    this.createMarkParticles();
    this.disableInteractive();
  }
  /**
   * Create particle effect for marking
   * Equivalent to particle effect in Unity
   */
  createMarkParticles() {
    const particles = this.scene.add.particles(this.x, this.y, "particle-star", {
      speed: { min: 70, max: 180 },
      scale: { start: 0.8, end: 0 },
      lifespan: 800,
      quantity: 20,
      tint: 58798,
      // Aqua color like the cell borders
      rotate: { min: 0, max: 360 },
      emitting: false,
      blendMode: "ADD"
    });
    particles.explode();
    const flash = this.scene.add.circle(0, 0, 45, 58798, 0.8);
    this.add(flash);
    this.scene.tweens.add({
      targets: flash,
      alpha: 0,
      scale: 1.8,
      duration: 500,
      ease: "Power2",
      onComplete: () => {
        flash.destroy();
      }
    });
    this.scene.time.delayedCall(1e3, () => {
      particles.destroy();
    });
  }
  /**
   * Create celebratory particle effect for win
   * Equivalent to PlayPickUpStar() when game ends
   */
  createWinParticles() {
    const particles = this.scene.add.particles(this.x, this.y, "particle-glow", {
      speed: { min: 30, max: 80 },
      scale: { start: 0.5, end: 0 },
      lifespan: 1500,
      quantity: 5,
      frequency: 200,
      tint: 58798,
      // Match the aqua color scheme
      blendMode: "ADD",
      rotate: { min: 0, max: 360 },
      emitting: true
    });
    this.scene.time.delayedCall(3e3, () => {
      particles.destroy();
    });
  }
}
class RightNumber extends Phaser$1.GameObjects.Container {
  rightIndex;
  letter;
  number;
  timerValue;
  timerTween;
  blinkTimer;
  timerFill;
  numberText;
  letterText;
  gameScene;
  // Store the GameScene reference
  constructor(scene, x, y, index, letter, number) {
    super(scene, x, y);
    this.scene = scene;
    this.gameScene = scene;
    this.rightIndex = index;
    this.letter = letter;
    this.number = number;
    this.name = `${letter}${number}`;
    this.timerValue = 1;
    this.timerTween = null;
    this.blinkTimer = null;
    this.createVisuals();
    this.startTimer();
    scene.add.existing(this);
    this.setScale(0);
    this.scene.tweens.add({
      targets: this,
      scale: 1,
      duration: 400,
      ease: "Back.Out",
      delay: 100
    });
  }
  createVisuals() {
    const cellSize = 85;
    const bgGraphics = this.scene.add.graphics();
    bgGraphics.fillGradientStyle(
      3172095,
      // Top-left: blue
      4674303,
      // Top-right: blue-purple
      6180351,
      // Bottom-right: more purple
      2187007,
      // Bottom-left: blue
      1
    );
    bgGraphics.fillCircle(0, 0, cellSize / 2);
    bgGraphics.lineStyle(3, 58798, 1);
    this.add(bgGraphics);
    this.timerFill = this.scene.add.graphics();
    this.add(this.timerFill);
    this.numberText = this.scene.add.text(
      0,
      5,
      // Slight offset for better visual balance
      this.number.toString(),
      {
        fontFamily: '"TT Neoris", Arial, sans-serif',
        fontSize: "38px",
        color: "#FFFFFF",
        align: "center",
        fontStyle: "bold",
        stroke: "#000000",
        strokeThickness: 2,
        shadow: { offsetX: 1, offsetY: 1, color: "#000000", blur: 2, fill: true }
      }
    ).setOrigin(0.5);
    this.add(this.numberText);
    this.letterText = this.scene.add.text(
      0,
      -22,
      this.letter,
      {
        fontFamily: '"TT Neoris", Arial, sans-serif',
        fontSize: "22px",
        color: "#FFFFFF",
        align: "center",
        fontStyle: "bold",
        stroke: "#000000",
        strokeThickness: 1
      }
    ).setOrigin(0.5);
    this.add(this.letterText);
    this.updateTimerVisual();
  }
  /**
   * Start countdown timer
   * Equivalent to timer() in Unity's RightNumberController
   */
  startTimer() {
    this.timerValue = 1;
    this.updateTimerVisual();
    this.timerTween = this.scene.tweens.add({
      targets: this,
      timerValue: 0,
      duration: 8500,
      onUpdate: () => {
        this.updateTimerVisual();
      },
      onComplete: () => {
        this.startBlinking();
      }
    });
  }
  /**
   * Update timer visual based on current timer value - enhanced to match Unity
   */
  updateTimerVisual() {
    this.timerFill.clear();
    if (this.timerValue <= 0) return;
    this.timerFill.lineStyle(4, 16777215, 0.3);
    this.timerFill.strokeCircle(0, 0, 42);
    const startAngle = -90 * (Math.PI / 180);
    const endAngle = startAngle + 360 * this.timerValue * (Math.PI / 180);
    this.timerFill.lineStyle(4, 58798, 1);
    this.timerFill.beginPath();
    this.timerFill.arc(0, 0, 42, startAngle, endAngle, false);
    this.timerFill.strokePath();
  }
  /**
   * Calculate score based on timer value
   * Equivalent to get_score() in Unity's RightNumberController
   */
  getScore() {
    if (this.timerValue >= 1) return 100;
    return Math.max(10, 100 - Math.floor((1 - this.timerValue) * 100));
  }
  /**
   * Start blinking matching bingo cell
   * Equivalent to startBlinking() in Unity's RightNumberController
   */
  startBlinking() {
    if (!this.gameScene || this.gameScene.gameEnd) return;
    try {
      const matchingCell = this.gameScene.findBingoCellByName(this.name);
      if (matchingCell && !matchingCell.marked) {
        matchingCell.blink();
        this.blinkTimer = this.scene.time.addEvent({
          delay: 1e3,
          callback: () => {
            try {
              if (!this.gameScene || this.gameScene.gameEnd) {
                if (this.blinkTimer) {
                  this.blinkTimer.remove();
                  this.blinkTimer = null;
                }
                return;
              }
              const cell = this.gameScene.findBingoCellByName(this.name);
              if (cell && !cell.marked) {
                cell.blink();
              } else {
                if (this.blinkTimer) {
                  this.blinkTimer.remove();
                  this.blinkTimer = null;
                }
              }
            } catch (error) {
              console.log("Error in blink timer callback:", error);
              if (this.blinkTimer) {
                this.blinkTimer.remove();
                this.blinkTimer = null;
              }
            }
          },
          loop: true
        });
      }
    } catch (error) {
      console.log("Error in startBlinking:", error);
    }
  }
  /**
   * Move this number down to a new position index
   * @param {number} newIndex - The new position index
   */
  moveToPosition(newIndex) {
    const oldIndex = this.rightIndex;
    this.rightIndex = newIndex;
    if (this.rightIndex < 0) {
      this.scene.tweens.add({
        targets: this,
        scale: 0,
        duration: 300,
        ease: "Back.In",
        onComplete: () => {
          this.destroy();
        }
      });
      return;
    }
    if (!this.gameScene) {
      this.destroy();
      return;
    }
    const position = this.gameScene.getRightPosition(this.rightIndex);
    this.scene.tweens.add({
      targets: this,
      x: position.x,
      duration: 400,
      ease: "Back.Out",
      easeParams: [1.5],
      onComplete: () => {
        if (this.rightIndex === 2 && oldIndex !== 2) {
          if (!this.gameScene) return;
          const matchingCell = this.gameScene.findBingoCellByName(this.name);
          if (matchingCell && !matchingCell.marked) {
            this.highlightMatchingCell(matchingCell);
          }
        }
      }
    });
  }
  /**
   * Create a highlight effect for the center position helper
   * @param {any} cell - The cell to highlight
   */
  highlightMatchingCell(cell) {
    try {
      if (!this.gameScene || !cell || cell.marked || this.gameScene.gameEnd) return;
      const highlight = this.scene.add.graphics();
      highlight.lineStyle(5, 16776960, 1);
      highlight.strokeRoundedRect(
        cell.x - 40,
        cell.y - 40,
        80,
        80,
        16
      );
      this.scene.tweens.add({
        targets: highlight,
        alpha: { from: 1, to: 0 },
        duration: 800,
        yoyo: true,
        repeat: 1,
        onComplete: () => {
          try {
            highlight.destroy();
          } catch (error) {
            console.log("Error destroying highlight:", error);
          }
        }
      });
      try {
        cell.blink();
      } catch (error) {
        console.log("Error blinking cell:", error);
      }
    } catch (error) {
      console.log("Error in highlightMatchingCell:", error);
    }
  }
  /**
   * Destroy this number properly, stopping all timers
   */
  destroy() {
    try {
      if (this.timerTween) {
        this.timerTween.stop();
        this.timerTween = null;
      }
      if (this.blinkTimer) {
        this.blinkTimer.remove();
        this.blinkTimer = null;
      }
      this.gameScene = null;
      super.destroy();
    } catch (error) {
      console.log("Error in RightNumber destroy:", error);
      try {
        super.destroy();
      } catch (e) {
        console.log("Error in super.destroy():", e);
      }
    }
  }
}
let ScoreManager$2 = class ScoreManager2 {
  scene;
  config;
  score;
  // UI Elements
  scoreText;
  scoreLabel;
  container;
  // Events
  events;
  constructor(scene, config = {}) {
    this.scene = scene;
    this.events = new Phaser$1.Events.EventEmitter();
    this.config = {
      initialScore: config.initialScore ?? 0,
      fontFamily: config.fontFamily ?? "Arial",
      fontSize: config.fontSize ?? "80px",
      labelFontSize: config.labelFontSize ?? "28px",
      scoreColor: config.scoreColor ?? "#33DDFF",
      labelColor: config.labelColor ?? "#FFFFFF",
      animationColor: config.animationColor ?? "#ffff00",
      animationDuration: config.animationDuration ?? 800
    };
    this.score = this.config.initialScore;
  }
  /**
   * Create the score UI elements at the specified position
   */
  createUI(x, y, parentContainer) {
    this.container = this.scene.add.container(0, 0);
    this.scoreLabel = this.scene.add.text(x, y - 30, "Total Point", {
      fontFamily: this.config.fontFamily,
      fontSize: this.config.labelFontSize,
      fontStyle: "bold",
      color: this.config.labelColor
    }).setOrigin(0.5);
    this.scoreText = this.scene.add.text(x, y + 30, this.score.toString(), {
      fontFamily: this.config.fontFamily,
      fontSize: this.config.fontSize,
      fontStyle: "bold",
      color: this.config.scoreColor
    }).setOrigin(0.5);
    this.container.add([this.scoreLabel, this.scoreText]);
    if (parentContainer) {
      parentContainer.add(this.container);
    }
  }
  /**
   * Add points to the score with optional animation
   */
  addPoints(points, animationConfig) {
    this.score += points;
    this.updateScoreDisplay();
    if (animationConfig) {
      this.createScoreAnimation(animationConfig);
    }
    this.events.emit("scoreChanged", this.score, points);
  }
  /**
   * Set the score to a specific value
   */
  setScore(newScore) {
    const oldScore = this.score;
    this.score = newScore;
    this.updateScoreDisplay();
    this.events.emit("scoreChanged", this.score, this.score - oldScore);
  }
  /**
   * Get the current score
   */
  getScore() {
    return this.score;
  }
  /**
   * Reset score to initial value
   */
  reset() {
    this.score = this.config.initialScore;
    this.updateScoreDisplay();
    this.events.emit("scoreReset", this.score);
  }
  /**
   * Update the score display text
   */
  updateScoreDisplay() {
    if (this.scoreText) {
      this.scoreText.setText(this.score.toString());
    }
  }
  /**
   * Create animated flying score text
   */
  createScoreAnimation(config) {
    const animationText = this.scene.add.text(
      config.startX,
      config.startY,
      `+${config.points}`,
      {
        fontFamily: this.config.fontFamily,
        fontSize: "24px",
        color: config.color ?? this.config.animationColor,
        stroke: "#000000",
        strokeThickness: 3
      }
    );
    animationText.setOrigin(0.5);
    this.scene.tweens.add({
      targets: animationText,
      y: config.startY - 50,
      alpha: 0,
      scale: 1.2,
      duration: config.duration ?? this.config.animationDuration,
      ease: "Power2",
      onComplete: () => {
        animationText.destroy();
      }
    });
  }
  /**
   * Subscribe to score events
   */
  on(event, callback) {
    this.events.on(event, callback);
  }
  /**
   * Unsubscribe from score events
   */
  off(event, callback) {
    this.events.off(event, callback);
  }
  /**
   * Clean up resources
   */
  destroy() {
    this.events.removeAllListeners();
    if (this.container) {
      this.container.destroy();
    }
    this.scoreText = void 0;
    this.scoreLabel = void 0;
    this.container = void 0;
  }
};
let TimerManager$2 = class TimerManager {
  scene;
  config;
  // Timer state
  startTime = 0;
  isRunning = false;
  isFinished = false;
  // UI Elements
  timeText;
  container;
  // Timer event
  timerEvent;
  // Events
  events;
  constructor(scene, config) {
    this.scene = scene;
    this.events = new Phaser$1.Events.EventEmitter();
    this.config = {
      duration: config.duration,
      updateInterval: config.updateInterval ?? 100,
      fontFamily: config.fontFamily ?? "Arial",
      fontSize: config.fontSize ?? "24px",
      normalColor: config.normalColor ?? "#FFFFFF",
      warningColor: config.warningColor ?? "#ff0000",
      warningThreshold: config.warningThreshold ?? 5
    };
  }
  /**
   * Create the timer UI at the specified position
   */
  createUI(x, y, parentContainer) {
    this.container = this.scene.add.container(0, 0);
    this.timeText = this.scene.add.text(x, y, this.formatTime(this.config.duration), {
      fontFamily: this.config.fontFamily,
      fontSize: this.config.fontSize,
      fontStyle: "bold",
      color: this.config.normalColor
    }).setOrigin(0.5);
    this.container.add(this.timeText);
    if (parentContainer) {
      parentContainer.add(this.container);
    }
  }
  /**
   * Start the countdown timer
   */
  start() {
    if (this.isRunning) return;
    this.startTime = this.scene.time.now;
    this.isFinished = false;
    this.isRunning = true;
    if (this.timerEvent) {
      this.timerEvent.destroy();
    }
    this.timerEvent = this.scene.time.addEvent({
      delay: this.config.updateInterval,
      callback: this.updateTimer.bind(this),
      loop: true
    });
    this.events.emit("timerStarted");
  }
  /**
   * Stop and reset the timer
   */
  stop() {
    if (this.timerEvent) {
      this.timerEvent.destroy();
      this.timerEvent = void 0;
    }
    this.events.emit("timerStopped");
  }
  /**
   * Get current timer state
   */
  getState() {
    const timeRemaining = this.getTimeRemaining();
    const progress = timeRemaining / this.config.duration;
    return {
      timeRemaining,
      totalDuration: this.config.duration,
      isRunning: this.isRunning,
      isFinished: this.isFinished,
      progress: Math.max(0, Math.min(1, progress))
    };
  }
  /**
   * Get time remaining in seconds
   */
  getTimeRemaining() {
    if (!this.isRunning) return this.config.duration;
    const elapsed = this.scene.time.now - this.startTime;
    return Math.max(0, this.config.duration - elapsed / 1e3);
  }
  /**
   * Update timer display and check for completion
   */
  updateTimer() {
    const timeRemaining = this.getTimeRemaining();
    const seconds = Math.ceil(timeRemaining);
    if (this.timeText) {
      this.timeText.setText(this.formatTime(seconds));
      const color = seconds <= this.config.warningThreshold ? this.config.warningColor : this.config.normalColor;
      this.timeText.setColor(color);
    }
    const state = this.getState();
    this.events.emit("timerUpdate", state);
    if (timeRemaining <= 0 && !this.isFinished) {
      this.isFinished = true;
      this.isRunning = false;
      if (this.timerEvent) {
        this.timerEvent.destroy();
        this.timerEvent = void 0;
      }
      this.events.emit("timeUp");
    }
  }
  /**
   * Format time in seconds to display string
   */
  formatTime(seconds) {
    return `${Math.max(0, Math.floor(seconds))}s`;
  }
  /**
   * Subscribe to timer events
   */
  on(event, callback) {
    this.events.on(event, callback);
  }
  /**
   * Unsubscribe from timer events
   */
  off(event, callback) {
    this.events.off(event, callback);
  }
  /**
   * Clean up resources
   */
  destroy() {
    this.stop();
    this.events.removeAllListeners();
    if (this.container) {
      this.container.destroy();
    }
    this.timeText = void 0;
    this.container = void 0;
  }
};
let LivesManager$2 = class LivesManager {
  scene;
  config;
  lives;
  // UI Elements
  hearts = [];
  container;
  // Events
  events;
  constructor(scene, config = {}) {
    this.scene = scene;
    this.events = new Phaser$1.Events.EventEmitter();
    this.config = {
      initialLives: config.initialLives ?? 3
    };
    this.lives = this.config.initialLives;
  }
  /**
   * Create the lives UI elements at the specified position
   */
  createUI(x, y, parentContainer) {
    this.container = this.scene.add.container(x, y);
    const totalWidth = (this.lives - 1) * 40;
    const startX = -totalWidth / 2;
    for (let i = 0; i < this.lives; i++) {
      let heart = this.scene.add.image(
        startX + i * 40,
        0,
        "heart"
      ).setOrigin(0.5).setScale(1.5);
      this.hearts.push(heart);
    }
    this.container.add(this.hearts);
    if (parentContainer) {
      parentContainer.add(this.container);
    }
  }
  deductHeart(fromX, fromY) {
    this.lives--;
    if (this.hearts[this.lives]) {
      this.hearts[this.lives].setTexture("heart_outline");
    }
    if (fromX !== void 0 && fromY !== void 0 && this.container) {
      this.createFlyingHeartAnimation(fromX, fromY);
    }
    this.events.emit("heartDeducted", this.lives);
  }
  /**
   * Create animated flying heart_broken image
   */
  createFlyingHeartAnimation(startX, startY) {
    if (!this.container) return;
    const flyingHeart = this.scene.add.image(startX, startY, "heart_broken").setOrigin(0.5).setScale(1.5).setAlpha(0.4);
    this.scene.tweens.add({
      targets: flyingHeart,
      y: startY - 200,
      scale: 3,
      alpha: 0.8,
      duration: 600,
      ease: "Power2",
      onComplete: () => {
        flyingHeart.destroy();
      }
    });
  }
  /**
   * Subscribe to score events
   */
  on(event, callback) {
    this.events.on(event, callback);
  }
  /**
   * Unsubscribe from score events
   */
  off(event, callback) {
    this.events.off(event, callback);
  }
  /**
   * Clean up resources
   */
  destroy() {
    this.events.removeAllListeners();
    if (this.container) {
      this.container.destroy();
    }
    this.container = void 0;
  }
};
let TimerBarUI$2 = class TimerBarUI {
  scene;
  config;
  // UI Elements
  container;
  timerBar;
  timerMask;
  timerIcon;
  leftCircle;
  rightCircle;
  background;
  // Gradient texture name
  gradientTextureName;
  constructor(scene, config) {
    this.scene = scene;
    this.gradientTextureName = `timerBarGradient_${Date.now()}_${Math.random()}`;
    this.config = {
      width: config.width,
      height: config.height,
      x: config.x,
      y: config.y,
      backgroundColor: config.backgroundColor ?? 1118481,
      gradientStartColor: config.gradientStartColor ?? "#33DDFF",
      gradientEndColor: config.gradientEndColor ?? "#664DFF",
      cornerRadius: config.cornerRadius ?? 10,
      showTimerIcon: config.showTimerIcon ?? true,
      showCircularBg: config.showCircularBg ?? true,
      circleRadius: config.circleRadius ?? 30,
      circleBorderColor: config.circleBorderColor ?? 3399167,
      circleBorderWidth: config.circleBorderWidth ?? 2
    };
  }
  /**
   * Create the timer bar UI elements
   */
  create(parentContainer) {
    this.container = this.scene.add.container(0, 0);
    this.background = this.scene.add.rectangle(
      this.config.x,
      this.config.y,
      this.config.width,
      this.config.height,
      this.config.backgroundColor
    ).setOrigin(0.5);
    this.container.add(this.background);
    this.createGradientTexture();
    this.timerBar = this.scene.add.image(this.config.x, this.config.y, this.gradientTextureName).setOrigin(0.5);
    this.container.add(this.timerBar);
    this.createTimerMask();
    if (this.config.showCircularBg) {
      this.createCircularBackgrounds();
    }
    if (this.config.showTimerIcon) {
      this.createTimerIcon();
    }
    if (parentContainer) {
      parentContainer.add(this.container);
    }
  }
  /**
   * Update the progress of the timer bar (0-1)
   */
  updateProgress(progress) {
    if (!this.timerMask || !this.timerBar) return;
    progress = Math.max(0, Math.min(1, progress));
    const barWidth = this.config.width - 4;
    const barHeight = this.config.height - 4;
    const visibleWidth = barWidth * progress;
    this.timerMask.clear();
    if (visibleWidth > 0) {
      this.timerMask.fillStyle(16777215, 1);
      this.timerMask.fillRoundedRect(
        this.config.x - barWidth / 2,
        this.config.y - barHeight / 2,
        visibleWidth,
        barHeight,
        this.config.cornerRadius
      );
    }
  }
  /**
   * Create the gradient texture for the timer bar
   */
  createGradientTexture() {
    const barWidth = this.config.width - 4;
    const barHeight = this.config.height - 4;
    const barTexture = this.scene.textures.createCanvas(this.gradientTextureName, barWidth, barHeight);
    const barContext = barTexture?.getContext();
    if (barContext && barTexture) {
      const gradient = barContext.createLinearGradient(0, 0, barWidth, 0);
      gradient.addColorStop(0, this.config.gradientStartColor);
      gradient.addColorStop(1, this.config.gradientEndColor);
      barContext.fillStyle = gradient;
      barContext.fillRect(0, 0, barWidth, barHeight);
      barTexture.refresh();
    }
  }
  /**
   * Create the mask for the timer bar progress
   */
  createTimerMask() {
    if (!this.timerBar) return;
    const barWidth = this.config.width - 4;
    const barHeight = this.config.height - 4;
    this.timerMask = this.scene.make.graphics({});
    this.timerMask.fillRect(
      this.config.x - barWidth / 2,
      this.config.y - barHeight / 2,
      barWidth,
      barHeight
    );
    const mask = new Phaser$1.Display.Masks.GeometryMask(this.scene, this.timerMask);
    this.timerBar.setMask(mask);
  }
  /**
   * Create circular backgrounds on left and right
   */
  createCircularBackgrounds() {
    if (!this.container) return;
    const leftX = this.config.x - this.config.width / 2;
    const rightX = this.config.x + this.config.width / 2;
    this.leftCircle = this.scene.add.circle(
      leftX,
      this.config.y,
      this.config.circleRadius,
      2236962
    );
    this.leftCircle.setStrokeStyle(this.config.circleBorderWidth, this.config.circleBorderColor, 1);
    this.container.add(this.leftCircle);
    this.rightCircle = this.scene.add.circle(
      rightX,
      this.config.y,
      this.config.circleRadius,
      2236962
    );
    this.rightCircle.setStrokeStyle(this.config.circleBorderWidth, 6704639, 1);
    this.container.add(this.rightCircle);
  }
  /**
   * Create timer icon
   */
  createTimerIcon() {
    if (!this.container) return;
    const leftX = this.config.x - this.config.width / 2;
    if (this.scene.textures.exists("timer_icon")) {
      this.timerIcon = this.scene.add.image(leftX, this.config.y, "timer_icon").setOrigin(0.5).setScale(0.5);
    } else {
      const clockGraphics = this.scene.add.graphics();
      clockGraphics.lineStyle(2, 3399167);
      clockGraphics.strokeCircle(0, 0, 12);
      clockGraphics.moveTo(0, 0);
      clockGraphics.lineTo(0, -8);
      clockGraphics.moveTo(0, 0);
      clockGraphics.lineTo(6, 0);
      clockGraphics.setPosition(leftX, this.config.y);
      this.container.add(clockGraphics);
    }
    if (this.timerIcon) {
      this.container.add(this.timerIcon);
    }
  }
  /**
   * Set the gradient colors
   */
  setGradientColors(startColor, endColor) {
    this.config.gradientStartColor = startColor;
    this.config.gradientEndColor = endColor;
    if (this.scene.textures.exists(this.gradientTextureName)) {
      this.scene.textures.remove(this.gradientTextureName);
    }
    this.createGradientTexture();
    if (this.timerBar) {
      this.timerBar.setTexture(this.gradientTextureName);
    }
  }
  /**
   * Get the container for additional positioning/effects
   */
  getContainer() {
    return this.container;
  }
  /**
   * Get the right circle position for timer text placement
   */
  getRightCirclePosition() {
    return {
      x: this.config.x + this.config.width / 2,
      y: this.config.y
    };
  }
  /**
   * Clean up resources
   */
  destroy() {
    if (this.scene.textures.exists(this.gradientTextureName)) {
      this.scene.textures.remove(this.gradientTextureName);
    }
    if (this.container) {
      this.container.destroy();
    }
    this.timerBar = void 0;
    this.timerMask = void 0;
    this.timerIcon = void 0;
    this.leftCircle = void 0;
    this.rightCircle = void 0;
    this.background = void 0;
    this.container = void 0;
  }
};
let SeededRandom$1 = class SeededRandom {
  seed;
  a = 1664525;
  // Multiplier (from Numerical Recipes)
  c = 1013904223;
  // Increment
  m = 2 ** 32;
  // Modulus (2^32)
  constructor(seed = Date.now()) {
    this.seed = seed >>> 0;
  }
  /**
   * Generate next random number between 0 and 1 (exclusive)
   */
  next() {
    this.seed = (this.a * this.seed + this.c) % this.m;
    return this.seed / this.m;
  }
  /**
   * Generate random integer between min and max (inclusive)
   */
  between(min, max) {
    return Math.floor(this.next() * (max - min + 1)) + min;
  }
  /**
   * Shuffle an array in place using Fisher-Yates algorithm
   */
  shuffle(array) {
    for (let i = array.length - 1; i > 0; i--) {
      const j = this.between(0, i);
      [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
  }
  /**
   * Get current seed value
   */
  getSeed() {
    return this.seed;
  }
  /**
   * Reset with new seed
   */
  setSeed(newSeed) {
    this.seed = newSeed >>> 0;
  }
};
const DEFAULT_PATTERN_SCORES = {
  horizontal: 500,
  vertical: 500,
  diagonal: 500,
  fullCard: 1e3
};
const BINGO_CONSTANTS = {
  GAME_DURATION: 60,
  INITIAL_CALLOUT_DELAY: 500,
  //1500
  CALLOUT_DELAY: 1800,
  //2400
  FREE_SPACE_POSITION: { row: 2, col: 2 },
  COLUMNS: ["B", "I", "N", "G", "O"],
  COLUMN_RANGES: {
    B: { min: 1, max: 15 },
    I: { min: 16, max: 30 },
    N: { min: 31, max: 45 },
    G: { min: 46, max: 60 },
    O: { min: 61, max: 75 }
  }
};
const PATTERN_DISPLAY_NAMES = {
  horizontal: "LINE BINGO!",
  vertical: "LINE BINGO!",
  diagonal: "DIAGONAL BINGO!",
  fullCard: "FULL CARD!"
};
let GameScene$2 = class GameScene2 extends Phaser$1.Scene {
  static BINGO_COLUMNS = BINGO_CONSTANTS.COLUMNS;
  static COLUMN_RANGES = BINGO_CONSTANTS.COLUMN_RANGES;
  static FREE_SPACE_POSITION = BINGO_CONSTANTS.FREE_SPACE_POSITION;
  // Game state properties
  gameEnd;
  // private rightTurnCount!: number;
  UIContainer;
  // Bingo-specific properties
  bingoCard;
  calledNumbers;
  availableNumbers;
  possibleNumbers;
  currentCallOrder;
  // Game objects
  bgoCells;
  rightNumbers;
  rightPositions;
  // UI elements
  countdownPanel;
  countdownText;
  scoreManager;
  timerManager;
  livesManager;
  timerBarUI;
  // Timers and events
  // private isRightItemScheduled: boolean;
  // Add camera dimensions for responsive layout
  cameraWidth;
  cameraHeight;
  centerX;
  centerY;
  // Seed-based random number generator
  rng;
  gameSeed;
  constructor(seed) {
    super("GameScene");
    this.gameSeed = seed ?? Date.now();
    this.rng = new SeededRandom$1(this.gameSeed);
    console.log(`Bingo Game initialized with seed: ${this.gameSeed}`);
  }
  init() {
    this.scoreManager = new ScoreManager$2(this, {
      initialScore: 0,
      fontSize: "80px",
      scoreColor: "#33DDFF"
    });
    this.timerManager = new TimerManager$2(this, {
      duration: BINGO_CONSTANTS.GAME_DURATION,
      warningThreshold: 5
    });
    this.livesManager = new LivesManager$2(this, {
      initialLives: 3
    });
    this.timerBarUI = new TimerBarUI$2(this, {
      width: this.cameras.main.width * 0.8,
      height: 35,
      x: this.cameras.main.width / 2,
      y: this.cameras.main.height * 0.07
    });
    this.timerManager.on("timeUp", () => this.endGame());
    this.timerManager.on("timerUpdate", (state) => {
      this.timerBarUI.updateProgress(state.progress);
    });
    this.livesManager.on("heartDeducted", (lives) => {
      if (lives === 0) {
        this.endGame();
      }
    });
  }
  create() {
    this.cameraWidth = this.cameras.main.width;
    this.cameraHeight = this.cameras.main.height;
    this.centerX = this.cameraWidth / 2;
    this.centerY = this.cameraHeight / 2;
    this.gameEnd = false;
    this.bgoCells = [];
    this.rightNumbers = [];
    this.calledNumbers = [];
    this.currentCallOrder = 0;
    this.availableNumbers = [];
    this.initializePossibleNumbers();
    this.rightPositions = this.calculateRightPositions();
    this.createBackground();
    this.createUI();
    this.startCountdown();
  }
  shutdown() {
    if (this.scoreManager) {
      this.scoreManager.destroy();
    }
    if (this.timerManager) {
      this.timerManager.destroy();
    }
    if (this.livesManager) {
      this.livesManager.destroy();
    }
    if (this.timerBarUI) {
      this.timerBarUI.destroy();
    }
  }
  /**
   * Create game background - using game_bg.png
   */
  createBackground() {
    this.add.image(0, 0, "game_background").setOrigin(0, 0).setDisplaySize(this.cameraWidth, this.cameraHeight);
  }
  /**
   * Create UI elements
   */
  createUI() {
    const { width, height } = this.cameras.main;
    this.UIContainer = this.add.container(0, 0);
    this.timerBarUI.create(this.UIContainer);
    const rightCirclePos = this.timerBarUI.getRightCirclePosition();
    this.timerManager.createUI(rightCirclePos.x, rightCirclePos.y, this.UIContainer);
    const timerBarY = height * 0.07;
    this.scoreManager.createUI(width / 2, timerBarY + 120, this.UIContainer);
    this.livesManager.createUI(width / 2, timerBarY + 50, this.UIContainer);
    this.createCountdownOverlay();
    this.hideGameUI();
  }
  /**
   * Create countdown overlay (initially hidden)
   * Using countdown images like in matching mayhem
   */
  createCountdownOverlay() {
    this.countdownPanel = this.add.container(0, 0);
    this.countdownPanel.setDepth(2);
    const overlay = this.add.rectangle(
      0,
      0,
      this.cameraWidth,
      this.cameraHeight,
      0,
      0.7
    ).setOrigin(0, 0);
    this.countdownPanel.add(overlay);
    this.countdownText = this.add.image(
      this.centerX,
      this.centerY,
      "countdown-3"
    ).setScale(0).setOrigin(0.5);
    this.countdownPanel.add(this.countdownText);
  }
  /**
   * Start countdown sequence
   * Using image-based countdown like in matching mayhem
   */
  async startCountdown() {
    this.countdownPanel.visible = true;
    const countdownImages = ["countdown-3", "countdown-2", "countdown-1", "countdown-go"];
    for (let i = 0; i < countdownImages.length; i++) {
      await this.playCountdownStep(countdownImages[i], i === countdownImages.length - 1);
    }
    this.countdownPanel.visible = false;
    this.showGameUI();
    this.initializeGame();
  }
  /**
   * Play a single step of the countdown animation
   */
  playCountdownStep(texture, isGo) {
    return new Promise((resolve) => {
      this.countdownText.setTexture(texture);
      this.countdownText.setScale(0);
      try {
        this.sound.play(isGo ? "go" : "countdown");
      } catch (error) {
        console.warn("Sound playback failed:", error);
      }
      this.tweens.add({
        targets: this.countdownText,
        scale: 0.2,
        // Same scale as matching mayhem
        duration: 300,
        ease: "Back.easeOut",
        onComplete: () => {
          this.time.delayedCall(700, () => {
            this.tweens.add({
              targets: this.countdownText,
              scale: 0,
              duration: 300,
              ease: "Back.easeIn",
              onComplete: () => resolve()
            });
          });
        }
      });
    });
  }
  /**
   * Initialize the game after countdown
   */
  initializeGame() {
    this.bingoCard = this.generateBingoCard();
    this.createBingoBoard();
    this.startGameTimer();
    this.time.delayedCall(BINGO_CONSTANTS.INITIAL_CALLOUT_DELAY, () => {
      this.addRightItem();
    });
  }
  initializePossibleNumbers() {
    this.possibleNumbers = [];
    for (let i = 1; i <= 75; i++) {
      this.possibleNumbers.push(i);
    }
  }
  generateBingoCard() {
    const card = [];
    for (let row = 0; row < 5; row++) {
      card[row] = [];
      for (let col = 0; col < 5; col++) {
        const column = GameScene2.BINGO_COLUMNS[col];
        if (row === GameScene2.FREE_SPACE_POSITION.row && col === GameScene2.FREE_SPACE_POSITION.col) {
          card[row][col] = {
            column,
            number: 0,
            // FREE space has no number
            row,
            col,
            isFree: true
          };
        } else {
          const availableInColumn = this.getAvailableNumbersForColumn(column, card);
          const randomIndex = this.rng.between(0, availableInColumn.length - 1);
          const number = availableInColumn[randomIndex];
          this.possibleNumbers.splice(number, 1);
          this.availableNumbers.push(number);
          card[row][col] = {
            column,
            number,
            row,
            col,
            isFree: false
          };
        }
      }
    }
    for (let i = 0; i < 10; i++) {
      const randomIndex = this.rng.between(0, this.possibleNumbers.length - 1);
      const number = this.possibleNumbers[randomIndex];
      this.possibleNumbers.splice(randomIndex, 1);
      this.availableNumbers.push(number);
    }
    this.rng.shuffle(this.availableNumbers);
    return card;
  }
  /**
   * Get available numbers for a specific column that haven't been used yet
  */
  getAvailableNumbersForColumn(column, currentCard) {
    const range = GameScene2.COLUMN_RANGES[column];
    const usedNumbers = /* @__PURE__ */ new Set();
    for (let row = 0; row < currentCard.length; row++) {
      const colIndex = GameScene2.BINGO_COLUMNS.indexOf(column);
      if (currentCard[row][colIndex] && !currentCard[row][colIndex].isFree) {
        usedNumbers.add(currentCard[row][colIndex].number);
      }
    }
    const available = [];
    for (let num = range.min; num <= range.max; num++) {
      if (!usedNumbers.has(num)) {
        available.push(num);
      }
    }
    return available;
  }
  /**
   * Create the bingo board with random numbers
   */
  createBingoBoard() {
    const cellSize = 80;
    const cellSpacing = 10;
    const totalWidth = (cellSize + cellSpacing) * 5 - cellSpacing;
    const startX = this.centerX - totalWidth / 2;
    const boardCenterY = this.centerY + 150;
    const headerY = boardCenterY - (cellSize + cellSpacing) * 2.5;
    const gridStartY = headerY + (cellSize + cellSpacing);
    for (let col = 0; col < 5; col++) {
      const x = startX + col * (cellSize + cellSpacing) + cellSize / 2;
      const y = headerY;
      const graphics = this.add.graphics();
      graphics.fillGradientStyle(
        3172095,
        // Top-left: blue
        4674303,
        // Top-right: blue-purple
        6180351,
        // Bottom-right: more purple
        2187007,
        // Bottom-left: blue
        1
      );
      graphics.lineStyle(3, 58798, 1);
      graphics.fillRoundedRect(x - cellSize / 2, y - cellSize / 2, cellSize, cellSize, 16);
      graphics.strokeRoundedRect(x - cellSize / 2, y - cellSize / 2, cellSize, cellSize, 16);
      this.add.text(
        x,
        y,
        GameScene2.BINGO_COLUMNS[col],
        {
          fontFamily: '"TT Neoris", Arial, sans-serif',
          fontSize: "48px",
          color: "#FFFFFF",
          align: "center",
          fontStyle: "bold",
          stroke: "#000000",
          strokeThickness: 3,
          shadow: { offsetX: 2, offsetY: 2, color: "#000000", blur: 2, fill: true }
        }
      ).setOrigin(0.5);
    }
    for (let row = 0; row < 5; row++) {
      for (let col = 0; col < 5; col++) {
        const x = startX + col * (cellSize + cellSpacing) + cellSize / 2;
        const y = gridStartY + row * (cellSize + cellSpacing);
        const cellData = this.bingoCard[row][col];
        const cell = new BingoCell(
          this,
          x,
          y,
          cellData.column,
          cellData.isFree ? 0 : cellData.number,
          // Use 0 for FREE space
          cellData.isFree
        );
        if (!cellData.isFree) {
          cell.letterText.alpha = 0;
        }
        cell.setScale(0);
        this.bgoCells.push(cell);
        this.time.delayedCall(150 * row + 50 * col, () => {
          this.tweens.add({
            targets: cell,
            scale: 1,
            duration: 300,
            ease: "Back.Out"
          });
        });
      }
    }
  }
  /**
   * Start the game timer
   * Equivalent to TimerCoroutine in Unity
   */
  startGameTimer() {
    this.timerManager.start();
  }
  /**
   * Calculate positions for right panel items
   */
  calculateRightPositions() {
    const positions = [];
    const cellSize = 80;
    const cellSpacing = 10;
    const totalWidth = (cellSize + cellSpacing) * 5 - cellSpacing;
    const startX = this.centerX - totalWidth / 2;
    const panelY = this.centerY - 180;
    for (let i = 0; i < 5; i++) {
      positions.push({
        x: startX + i * (cellSize + cellSpacing) + cellSize / 2,
        y: panelY
      });
    }
    return positions;
  }
  /**
   * Get position for right panel item by index
   * @param {number} index - Position index
   * @returns {object} Position with x and y coordinates
   */
  getRightPosition(index) {
    if (index >= 0 && index < this.rightPositions.length) {
      return this.rightPositions[index];
    }
    return { x: this.centerX + 100, y: this.centerY };
  }
  callNextBingoNumber() {
    if (this.availableNumbers.length === 0) {
      return null;
    }
    const number = this.availableNumbers.shift();
    const column = this.getColumnForNumber(number);
    const calledNumber = {
      column,
      number,
      callOrder: ++this.currentCallOrder,
      timestamp: Date.now()
    };
    this.calledNumbers.push(calledNumber);
    return calledNumber;
  }
  getColumnForNumber(number) {
    if (number >= 1 && number <= 15) return "B";
    if (number >= 16 && number <= 30) return "I";
    if (number >= 31 && number <= 45) return "N";
    if (number >= 46 && number <= 60) return "G";
    if (number >= 61 && number <= 75) return "O";
    throw new Error(`Invalid bingo number: ${number}`);
  }
  /**
   * Add a new number to the right panel using standard bingo calling
   * Equivalent to addRightItem in Unity
   */
  addRightItem() {
    if (this.gameEnd) return;
    const calledNumber = this.callNextBingoNumber();
    if (!calledNumber) {
      return;
    }
    this.sound.play("number-appear");
    this.moveExistingRightItems();
    const position = this.getRightPosition(4);
    const rightNumber = new RightNumber(
      this,
      position.x,
      position.y,
      4,
      calledNumber.column,
      calledNumber.number
    );
    this.rightNumbers.push(rightNumber);
    this.time.delayedCall(BINGO_CONSTANTS.CALLOUT_DELAY, () => {
      if (!this.gameEnd) {
        this.addRightItem();
      }
    });
  }
  /**
   * Move existing right items down
   */
  moveExistingRightItems() {
    for (const item of this.rightNumbers) {
      item.moveToPosition(item.rightIndex - 1);
    }
    this.rightNumbers = this.rightNumbers.filter((item) => item.rightIndex >= 0);
  }
  /**
   * Check if a cell matches any right panel item
   * @param {BingoCell} cell - The cell to check
   */
  checkForMatch(cell) {
    if (this.gameEnd) return;
    const matchingNumber = this.rightNumbers.find((item) => item.name === cell.name);
    if (matchingNumber) {
      cell.mark();
      this.children.list.forEach((child) => {
        if (child.type === "Graphics" && child.x === cell.x - 40 && child.y === cell.y - 40) {
          child.destroy();
        }
      });
      this.scoreManager.addPoints(matchingNumber.getScore(), {
        startX: this.centerX,
        startY: 80,
        points: matchingNumber.getScore()
      });
      this.sound.play("match");
      this.rightNumbers = this.rightNumbers.filter((item) => item !== matchingNumber);
      matchingNumber.destroy();
      const winResult = this.checkForWinningPatterns();
      console.log("Win result:", winResult);
      if (winResult.hasWon && winResult.pattern === "fullCard") {
        this.handleBingoWin(winResult);
        return;
      }
    } else {
      this.sound.play("wrong");
      this.livesManager.deductHeart(
        cell.x,
        cell.y
      );
    }
  }
  /**
   * Handle a bingo win
   */
  handleBingoWin(winResult) {
    if (this.gameEnd) return;
    this.gameEnd = true;
    this.timerManager.stop();
    const bonusScore = DEFAULT_PATTERN_SCORES[winResult.pattern] || 0;
    this.scoreManager.addPoints(bonusScore, {
      startX: this.centerX,
      startY: 80,
      points: bonusScore
    });
    this.createWinAnnouncement(winResult);
    this.createCelebrationEffects();
    this.time.delayedCall(1500, () => {
      this.scene.start(
        "GameEndScene",
        {
          score: this.scoreManager.getScore(),
          winPattern: "timeout"
        }
      );
    });
  }
  /**
   * Create win announcement display
   */
  createWinAnnouncement(winResult) {
    const announcement = this.add.text(
      this.centerX,
      this.centerY - 100,
      PATTERN_DISPLAY_NAMES[winResult.pattern],
      {
        fontFamily: '"TT Neoris", Arial, sans-serif',
        fontSize: "64px",
        color: "#FFD700",
        align: "center",
        fontStyle: "bold",
        stroke: "#000000",
        strokeThickness: 4,
        shadow: { offsetX: 3, offsetY: 3, color: "#000000", blur: 5, fill: true }
      }
    ).setOrigin(0.5).setDepth(10);
    announcement.setScale(0);
    this.tweens.add({
      targets: announcement,
      scale: 1,
      duration: 500,
      ease: "Back.Out",
      onComplete: () => {
        this.tweens.add({
          targets: announcement,
          scale: { from: 1, to: 1.1 },
          duration: 800,
          yoyo: true,
          repeat: -1,
          ease: "Sine.easeInOut"
        });
      }
    });
  }
  /**
   * Check for winning patterns on the bingo card
   * Returns the first winning pattern found, if any
   */
  checkForWinningPatterns() {
    if (this.isFullCardComplete()) {
      const winningCells = this.bingoCard.flat();
      return {
        hasWon: true,
        pattern: "fullCard",
        winningCells
      };
    }
    return { hasWon: false };
  }
  // /**
  //  * Check if a specific row is complete
  //  */
  // private isRowComplete(row: number): boolean {
  //   for (let col = 0; col < 5; col++) {
  //     const cellData = this.bingoCard[row][col];
  //     const cell = this.findBingoCellByPosition(row, col);
  //     if (!cell || (!cell.marked && !cellData.isFree)) {
  //       return false;
  //     }
  //   }
  //   return true;
  // }
  // /**
  //  * Check if a specific column is complete
  //  */
  // private isColumnComplete(col: number): boolean {
  //   for (let row = 0; row < 5; row++) {
  //     const cellData = this.bingoCard[row][col];
  //     const cell = this.findBingoCellByPosition(row, col);
  //     if (!cell || (!cell.marked && !cellData.isFree)) {
  //       return false;
  //     }
  //   }
  //   return true;
  // }
  // /**
  //  * Check if a diagonal is complete
  //  */
  // private isDiagonalComplete(type: 'main' | 'anti'): boolean {
  //   for (let i = 0; i < 5; i++) {
  //     const row = i;
  //     const col = type === 'main' ? i : 4 - i;
  //     const cellData = this.bingoCard[row][col];
  //     const cell = this.findBingoCellByPosition(row, col);
  //     if (!cell || (!cell.marked && !cellData.isFree)) {
  //       return false;
  //     }
  //   }
  //   return true;
  // }
  /**
   * Check if the full card is complete
   */
  isFullCardComplete() {
    for (let row = 0; row < 5; row++) {
      for (let col = 0; col < 5; col++) {
        const cellData = this.bingoCard[row][col];
        const cell = this.findBingoCellByPosition(row, col);
        if (!cell || !cell.marked && !cellData.isFree) {
          return false;
        }
      }
    }
    return true;
  }
  /**
   * Find a bingo cell by its grid position
   */
  findBingoCellByPosition(row, col) {
    const index = row * 5 + col;
    return this.bgoCells[index] || null;
  }
  /**
   * Find a bingo cell by name
   * @param {string} name - The name of the cell (e.g., "B12")
   * @returns {BingoCell|null} The matching cell or null if not found
   */
  findBingoCellByName(name) {
    return this.bgoCells.find((cell) => cell.name === name) || null;
  }
  /**
   * End the game (called when timer runs out or no more numbers available)
   * Equivalent to when GameEnd is set to true in Unity
   */
  endGame() {
    if (this.gameEnd) return;
    this.gameEnd = true;
    this.timerManager.stop();
    this.createCelebrationEffects();
    this.time.delayedCall(1500, () => {
      this.scene.start("GameEndScene", { score: this.scoreManager.getScore(), winPattern: "timeout" });
    });
  }
  /**
   * Create timeout announcement when game ends without bingo
   */
  // private createTimeoutAnnouncement(): void {
  //   const announcement = this.add.text(
  //     this.centerX,
  //     this.centerY - 100,
  //     'TIME\'S UP!',
  //     {
  //       fontFamily: '"TT Neoris", Arial, sans-serif',
  //       fontSize: '64px',
  //       color: '#FF6B6B',
  //       align: 'center',
  //       fontStyle: 'bold',
  //       stroke: '#000000',
  //       strokeThickness: 4,
  //       shadow: { offsetX: 3, offsetY: 3, color: '#000000', blur: 5, fill: true }
  //     }
  //   ).setOrigin(0.5).setDepth(10);
  //   // Animate the announcement
  //   announcement.setScale(0);
  //   this.tweens.add({
  //     targets: announcement,
  //     scale: 1,
  //     duration: 500,
  //     ease: 'Back.Out'
  //   });
  // }
  /**
   * Create celebration effects for game end
   * Equivalent to HandleGameEnd in Unity
   */
  createCelebrationEffects() {
    this.sound.play("win");
    for (const cell of this.bgoCells) {
      if (!cell.marked) {
        cell.mark();
      }
      this.time.delayedCall(this.rng.between(0, 1e3), () => {
        cell.createWinParticles();
      });
    }
  }
  /**
   * Hide game UI elements during countdown
   */
  hideGameUI() {
    this.UIContainer.setVisible(false);
  }
  /**
   * Show game UI elements after countdown
  */
  showGameUI() {
    this.UIContainer.setVisible(true);
  }
};
let GameEndScene$2 = class GameEndScene2 extends Phaser.Scene {
  ticTaps;
  score = 0;
  backToLobbyButton;
  constructor() {
    super("GameEndScene");
  }
  init(data) {
    this.score = data.score || 0;
  }
  create() {
    this.ticTaps = new TicTapsConnector$2();
    this.add.image(0, 0, "game_background").setOrigin(0, 0).setDisplaySize(this.cameras.main.width, this.cameras.main.height);
    const panelWidth = this.cameras.main.width * 0.8;
    const panelHeight = this.cameras.main.height * 0.6;
    if (this.sys.game.renderer.type === Phaser.WEBGL) {
      const blurBg = this.add.graphics();
      blurBg.fillStyle(0, 0.3);
      blurBg.fillRoundedRect(
        this.cameras.main.width / 2 - panelWidth / 2 - 2,
        this.cameras.main.height / 2 - panelHeight / 2 - 2,
        panelWidth + 4,
        panelHeight + 4,
        20
      );
      blurBg.postFX.addBlur(0, 0, 1, 2, 1, 1);
    }
    const panel = this.add.graphics();
    panel.fillStyle(1712945, 0.4);
    panel.fillRoundedRect(
      this.cameras.main.width / 2 - panelWidth / 2,
      this.cameras.main.height / 2 - panelHeight / 2,
      panelWidth,
      panelHeight,
      20
    );
    const gameOverImage = this.add.image(
      this.cameras.main.width / 2,
      this.cameras.main.height / 2 - panelHeight * 0.5,
      // Positioned at top of panel
      "game_over"
    ).setOrigin(0.5);
    const gameOverScale = panelWidth * 0.8 / gameOverImage.width;
    gameOverImage.setScale(gameOverScale);
    if (this.sys.game.renderer.type === Phaser.WEBGL) {
      gameOverImage.postFX.addGlow(4980654, 1, 0, false, 0.1, 15);
    }
    this.add.text(
      this.cameras.main.width / 2,
      this.cameras.main.height / 2 - 100,
      // Slight offset above center
      "SCORE",
      {
        fontFamily: "Arial",
        fontSize: "30px",
        fontStyle: "bold",
        color: "#FFFFFF"
      }
    ).setOrigin(0.5);
    this.createGradientText(
      this.cameras.main.width / 2,
      this.cameras.main.height / 2,
      this.score.toString(),
      90,
      true
    );
    this.createBackToLobbyButton();
  }
  /**
   * Create the Back to Lobby button with gradient border as in Image 2
   */
  createBackToLobbyButton() {
    const buttonWidth = this.cameras.main.width * 0.7;
    const buttonHeight = 80;
    const buttonX = this.cameras.main.width / 2;
    const buttonY = this.cameras.main.height / 2 + this.cameras.main.height * 0.2;
    this.backToLobbyButton = this.add.container(buttonX, buttonY);
    const borderCanvas = this.textures.createCanvas("buttonBorder", buttonWidth + 4, buttonHeight + 4);
    if (borderCanvas) {
      const borderContext = borderCanvas.getContext();
      const gradient = borderContext.createLinearGradient(0, 0, buttonWidth + 4, 0);
      gradient.addColorStop(0, "#32c4ff");
      gradient.addColorStop(0.5, "#7f54ff");
      gradient.addColorStop(1, "#b63efc");
      borderContext.strokeStyle = gradient;
      borderContext.lineWidth = 2.5;
      roundRect$2(borderContext, 2, 2, buttonWidth, buttonHeight, 18);
      borderCanvas.refresh();
      const border = this.add.image(0, 0, "buttonBorder").setOrigin(0.5);
      this.backToLobbyButton.add(border);
    }
    const buttonBg = this.add.graphics();
    buttonBg.fillStyle(1185311, 1);
    buttonBg.fillRoundedRect(-buttonWidth / 2 + 2, -buttonHeight / 2 + 2, buttonWidth - 4, buttonHeight - 4, 16);
    this.backToLobbyButton.add(buttonBg);
    if (this.textures.exists("back_to_lobby")) {
      const buttonText = this.add.image(0, 0, "back_to_lobby").setOrigin(0.5);
      const textScale = Math.min(buttonWidth * 0.7 / buttonText.width, buttonHeight * 0.6 / buttonText.height);
      buttonText.setScale(textScale);
      this.backToLobbyButton.add(buttonText);
    } else {
      const buttonText = this.createGradientText(0, 0, "BACK TO LOBBY", 28, false, true);
      this.backToLobbyButton.add(buttonText);
    }
    const hitArea = new Phaser.Geom.Rectangle(-buttonWidth / 2, -buttonHeight / 2, buttonWidth, buttonHeight);
    this.backToLobbyButton.setInteractive(hitArea, Phaser.Geom.Rectangle.Contains);
    this.backToLobbyButton.on("pointerover", () => {
      this.backToLobbyButton.setScale(1.05);
    });
    this.backToLobbyButton.on("pointerout", () => {
      this.backToLobbyButton.setScale(1);
    });
    this.backToLobbyButton.on("pointerdown", () => {
      if (this.sound.get("laser")) {
        this.sound.play("laser", { volume: 0.7 });
      } else if (this.sound.get("countdown")) {
        this.sound.play("countdown", { volume: 0.7 });
      }
      this.backToLobbyButton.setScale(0.95);
      this.time.delayedCall(100, () => {
        this.backToLobbyButton.setScale(1);
        this.endGame();
      });
    });
  }
  /**
   * Helper function to create gradient text
   */
  createGradientText(x, y, text, fontSize = 32, isScoreText = false, isButtonText = false) {
    const textureName = "gradientText-" + text.replace(/\s+/g, "-") + "-" + fontSize + (isScoreText ? "-score" : "") + (isButtonText ? "-button" : "");
    if (this.textures.exists(textureName)) {
      this.textures.remove(textureName);
    }
    const width = Math.max(400, text.length * fontSize * 0.7);
    const height = fontSize * 1.5;
    const textCanvas = this.textures.createCanvas(textureName, width, height);
    if (!textCanvas) {
      console.error("Failed to create gradient text canvas");
      return this.add.image(x, y, "").setOrigin(0.5);
    }
    const context = textCanvas.getContext();
    const gradient = context.createLinearGradient(0, 0, width, height * 0.5);
    if (isScoreText) {
      gradient.addColorStop(0, "#4cffae");
      gradient.addColorStop(0.4, "#32c4ff");
      gradient.addColorStop(1, "#5c67ff");
    } else if (isButtonText) {
      gradient.addColorStop(0, "#32c4ff");
      gradient.addColorStop(0.5, "#7f54ff");
      gradient.addColorStop(1, "#b63efc");
    } else {
      gradient.addColorStop(0, "#33DDFF");
      gradient.addColorStop(1, "#664DFF");
    }
    context.font = `bold ${fontSize}px Arial`;
    context.textAlign = "center";
    context.textBaseline = "middle";
    if (isScoreText) {
      context.strokeStyle = "rgba(255, 255, 255, 0.9)";
      context.lineWidth = 5;
      context.strokeText(text, width / 2, height / 2);
    }
    context.fillStyle = gradient;
    context.fillText(text, width / 2, height / 2);
    textCanvas.refresh();
    return this.add.image(x, y, textureName).setOrigin(0.5);
  }
  endGame() {
    const flash = this.add.rectangle(
      this.cameras.main.width / 2,
      this.cameras.main.height / 2,
      this.cameras.main.width,
      this.cameras.main.height,
      16777215
    ).setAlpha(0).setOrigin(0.5);
    flash.setDepth(1e3);
    this.ticTaps.sendScore(this.score);
    this.ticTaps.notifyGameQuit();
    this.tweens.add({
      targets: flash,
      alpha: 0.8,
      duration: 100,
      ease: "Sine.easeOut",
      onComplete: () => {
        this.tweens.add({
          targets: flash,
          alpha: 0,
          delay: 50,
          duration: 250,
          ease: "Sine.easeIn",
          onComplete: () => {
            this.scene.start("GameStartScene");
          }
        });
      }
    });
  }
};
function roundRect$2(ctx, x, y, width, height, radius, fill, stroke) {
  ctx.beginPath();
  ctx.moveTo(x + radius, y);
  ctx.lineTo(x + width - radius, y);
  ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
  ctx.lineTo(x + width, y + height - radius);
  ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
  ctx.lineTo(x + radius, y + height);
  ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
  ctx.lineTo(x, y + radius);
  ctx.quadraticCurveTo(x, y, x + radius, y);
  ctx.closePath();
  {
    ctx.stroke();
  }
}
const GAME_CONFIG = {
  BACKGROUND_COLOR: "#0E0F1E",
  GAME_TIME: 20,
  ROUND_TIME: 5,
  CARD_SIZE: 150,
  ANIMAL_COUNT: 4,
  COLOR_COUNT: 3,
  OPTION_CARDS_COUNT: 5
};
const ANIMAL_NAMES = ["eagle", "koala", "wolf", "dog"];
const COLOR_NAMES = ["cyan", "green", "yellow"];
const CATEGORY_TINTS = {
  0: 6750207,
  // Cyan
  1: 6750054,
  // Green
  2: 16777062
  // Yellow
};
let PreloadScene$1 = class PreloadScene3 extends Phaser.Scene {
  // 2D array to store animal images by [animal][color]
  animalImages = [];
  constructor() {
    super("PreloadScene");
  }
  preload() {
    const { width, height } = this.cameras.main;
    const bgBar = this.add.rectangle(
      width / 2,
      height / 2,
      width / 2,
      20,
      2302755
    );
    const progressBar = this.add.rectangle(
      bgBar.x - bgBar.width / 2,
      bgBar.y,
      0,
      bgBar.height,
      65280
    );
    progressBar.setOrigin(0, 0.5);
    const loadingText = this.add.text(
      width / 2,
      height / 2 - 30,
      "Loading...",
      {
        font: "24px Arial",
        color: "#ffffff"
      }
    ).setOrigin(0.5);
    this.load.on("progress", (value) => {
      progressBar.width = bgBar.width * value;
    });
    this.load.on("complete", () => {
      progressBar.destroy();
      bgBar.destroy();
      loadingText.destroy();
    });
    this.load.image("card_bg", "/assets-match-mayhem/images/card_bg.svg");
    this.load.image("card_correct_bg", "/assets-match-mayhem/images/card_correct_bg.png");
    this.load.image("card_incorrect_bg", "/assets-match-mayhem/images/card_incorrect_bg.png");
    this.load.image("timer_bg", "/assets/images/timer_bg.svg");
    this.load.image("timer_icon", "/assets/images/timer_icon.png");
    this.load.image("timer_countdown_bg", "/assets/images/timer_countdown_bg.png");
    this.load.svg("heart", "/assets/images/mdi--heart.svg");
    this.load.svg("heart_outline", "/assets/images/mdi-light--heart.svg");
    this.load.svg("heart_broken", "/assets/images/mdi--heart-broken.svg");
    this.load.image("game_name", "/assets-match-mayhem/images/game_name.svg");
    this.load.image("game_background", "/assets/images/game_bg.png");
    this.load.image("button_bg", "/assets/images/button_bg.svg");
    this.load.image("game_start", "/assets/images/game_start.png");
    this.load.image("game_over", "/assets/images/game_over.svg");
    this.load.image("back_to_lobby", "/assets/images/back_to_lobby.png");
    for (let animalIndex = 0; animalIndex < GAME_CONFIG.ANIMAL_COUNT; animalIndex++) {
      this.animalImages[animalIndex] = [];
    }
    for (let animalIndex = 0; animalIndex < GAME_CONFIG.ANIMAL_COUNT; animalIndex++) {
      for (let colorCategory = 0; colorCategory < GAME_CONFIG.COLOR_COUNT; colorCategory++) {
        const imageKey = `image_${colorCategory}_${animalIndex}`;
        this.load.image(imageKey, `/assets-match-mayhem/images/${colorCategory}/${animalIndex}.png`);
        this.animalImages[animalIndex][colorCategory] = imageKey;
      }
    }
    this.load.audio("countdown", ["/assets/audio/countdown.mp3", "/assets/audio/countdown.wav"]);
    this.load.audio("go", ["/assets/audio/go.mp3", "/assets/audio/go.wav"]);
    this.load.audio("wrong", ["/assets/audio/wrong.mp3", "/assets/audio/wrong.wav"]);
    this.load.audio("end", ["/assets-match-mayhem/sounds/end.mp3", "/assets-match-mayhem/sounds/end.wav"]);
    this.load.audio("correct", ["/assets-match-mayhem/sounds/correct.mp3", "/assets-match-mayhem/sounds/correct.wav"]);
    this.load.audio("round", ["/assets-match-mayhem/sounds/round.mp3", "/assets-match-mayhem/sounds/round.wav"]);
    this.load.audio("laser", ["/assets-match-mayhem/sounds/laser.mp3", "/assets-match-mayhem/sounds/laser.wav"]);
    this.load.image("countdown-3", "/assets/images/countdown-3.png");
    this.load.image("countdown-2", "/assets/images/countdown-2.png");
    this.load.image("countdown-1", "/assets/images/countdown-1.png");
    this.load.image("countdown-go", "/assets/images/countdown-go.png");
    this.load.bitmapFont("game_font", "/assets-match-mayhem/fonts/font.png", "/assets-match-mayhem/fonts/font.xml");
  }
  /**
   * Get the image key for a specific animal and color
   * @param animalIndex The index of the animal (0-3)
   * @param colorIndex The index of the color (0-2)
   * @returns The image key
   */
  getAnimalImageKey(animalIndex, colorIndex) {
    if (animalIndex >= 0 && animalIndex < this.animalImages.length && colorIndex >= 0 && colorIndex < this.animalImages[animalIndex].length) {
      return this.animalImages[animalIndex][colorIndex];
    }
    console.error(`Invalid animal or color index: ${animalIndex}, ${colorIndex}`);
    return "";
  }
  create() {
    this.game.registry.set("animalImages", this.animalImages);
    this.time.delayedCall(500, () => {
      this.cameras.main.setBackgroundColor("#0E0F1E");
      this.scene.start("GameStartScene");
    });
  }
};
let TicTapsConnector$1 = class TicTapsConnector3 {
  isWebGL;
  constructor() {
    this.isWebGL = this.checkIfWebGL();
  }
  /**
   * Check if running in a browser environment and embedded
   */
  checkIfWebGL() {
    return typeof window !== "undefined" && window.parent && window.parent !== window;
  }
  /**
   * Notify parent window that the game is ready
   * Equivalent to TicTaps.Instance.NotifyGameReady()
   */
  notifyGameReady() {
    this.sendMessage({ type: "gameReady" });
  }
  /**
   * Send score to parent window
   * Equivalent to TicTaps.Instance.SendScore()
   */
  sendScore(score) {
    this.sendMessage({ type: "gameScore", score });
  }
  /**
   * Notify parent window that the game has quit
   * Equivalent to TicTaps.Instance.NotifyGameQuit()
   */
  notifyGameQuit() {
    this.sendMessage({ type: "gameQuit" });
  }
  /**
  * Send a typed message to the parent window
  */
  sendMessage(message) {
    if (this.isWebGL && window.parent && typeof window.parent.postMessage === "function") {
      window.parent.postMessage(message, "*");
      console.log("Message sent to parent:", message);
    }
  }
};
let GameStartScene$1 = class GameStartScene3 extends Phaser.Scene {
  ticTaps;
  startButton;
  isStarting = false;
  constructor() {
    super("GameStartScene");
  }
  create() {
    const { width, height } = this.cameras.main;
    this.ticTaps = new TicTapsConnector$1();
    this.add.image(0, 0, "game_background").setOrigin(0, 0).setDisplaySize(width, height);
    const gameTitle = this.add.image(
      width / 2,
      height * 0.25,
      // Positioned at about 25% from the top
      "game_name"
    ).setOrigin(0.5);
    const titleScale = (
      // (width * 0.6) / gameTitle.width;
      Math.min(width * 0.7 / gameTitle.width, 0.8)
    );
    gameTitle.setScale(titleScale);
    this.tweens.add({
      targets: gameTitle,
      scaleX: titleScale * 1.02,
      scaleY: titleScale * 1.02,
      duration: 1500,
      yoyo: true,
      repeat: -1,
      ease: "Sine.easeInOut"
    });
    this.startButton = this.add.image(
      width / 2,
      height * 0.6,
      // Positioned at about 60% from the top
      "button_bg"
    ).setOrigin(0.5);
    const buttonScale = (
      // (width * 0.6) / this.startButton.width;
      Math.min(width * 0.6 / this.startButton.width, 0.4)
    );
    this.startButton.setScale(buttonScale);
    const startText = this.add.image(
      this.startButton.x,
      this.startButton.y - 5,
      "game_start"
    ).setOrigin(0.5);
    const textScale = this.startButton.displayWidth * 0.6 / startText.width;
    startText.setScale(textScale);
    this.startButton.setInteractive({ useHandCursor: true });
    this.startButton.on("pointerover", () => {
      this.tweens.add({
        targets: [this.startButton, startText],
        scaleX: buttonScale * 1.05,
        scaleY: buttonScale * 1.05,
        duration: 150,
        ease: "Sine.easeOut"
      });
    });
    this.startButton.on("pointerout", () => {
      this.tweens.add({
        targets: [this.startButton, startText],
        scaleX: buttonScale,
        scaleY: buttonScale,
        duration: 150,
        ease: "Sine.easeOut"
      });
    });
    this.startButton.on("pointerdown", () => {
      if (this.sound.get("countdown")) {
        this.sound.play("countdown", { volume: 0.7 });
      }
      this.ticTaps.notifyGameReady();
      this.tweens.add({
        targets: [this.startButton, startText],
        scaleX: buttonScale * 0.95,
        scaleY: buttonScale * 0.95,
        duration: 100,
        yoyo: true,
        onComplete: () => this.startGameCountdown(width, height)
      });
    });
  }
  startGameCountdown(width, height) {
    if (this.isStarting) return;
    this.isStarting = true;
    const flash = this.add.rectangle(
      width / 2,
      height / 2,
      width,
      height,
      16777215
    ).setAlpha(0).setOrigin(0.5);
    flash.setDepth(1e3);
    this.tweens.add({
      targets: flash,
      alpha: 0.8,
      duration: 100,
      ease: "Sine.easeOut",
      onComplete: () => {
        if (this.sound.get("go")) {
          this.sound.play("go", { volume: 0.7 });
        }
        this.tweens.add({
          targets: flash,
          alpha: 0,
          delay: 50,
          // Hold for 50ms
          duration: 250,
          ease: "Sine.easeIn",
          onComplete: () => {
            this.scene.start("GameScene");
          }
        });
      }
    });
  }
};
class MatchingCard extends Phaser.GameObjects.Container {
  cardImage;
  cardBackground;
  isCorrect = false;
  isSelected = false;
  cardSize;
  constructor(scene, x, y, size = 120) {
    super(scene, x, y);
    this.cardSize = size;
    this.cardBackground = scene.add.image(0, 0, "card_bg").setOrigin(0.5);
    this.cardBackground.displayWidth = size;
    this.cardBackground.displayHeight = size;
    this.add(this.cardBackground);
    this.cardImage = scene.add.image(0, 0, "");
    this.cardImage.setScale(0);
    this.add(this.cardImage);
    this.setSize(size, size);
    this.setInteractive({ useHandCursor: true });
    this.on("pointerover", this.onPointerOver, this);
    this.on("pointerout", this.onPointerOut, this);
    scene.add.existing(this);
  }
  /**
   * Set the tint color for the card image
   */
  setTint(color) {
    this.cardImage.setTint(color);
  }
  /**
   * Set the card's image
   */
  setCardImage(imageKey) {
    this.setData("imageKey", imageKey);
    if (!imageKey || imageKey === "") {
      this.cardImage.setTexture("");
      this.cardImage.setVisible(false);
      return;
    }
    try {
      this.cardImage.setTexture(imageKey);
      this.cardImage.setVisible(true);
      this.cardImage.setScale(0);
      this.scene.time.delayedCall(10, () => {
        const cardWidth = this.cardSize * 0.7;
        const cardHeight = this.cardSize * 0.7;
        const imgWidth = this.cardImage.width;
        const imgHeight = this.cardImage.height;
        const cardRatio = cardWidth / cardHeight;
        const imgRatio = imgWidth / imgHeight;
        let scale = 1;
        if (imgRatio > cardRatio) {
          scale = cardWidth / imgWidth;
        } else {
          scale = cardHeight / imgHeight;
        }
        this.cardImage.setData("targetScale", scale);
      });
    } catch (error) {
      console.error(`Failed to set texture for ${imageKey}`, error);
      this.cardImage.setTexture("");
      this.cardImage.setVisible(false);
    }
  }
  /**
   * Animate the card image appearing
   */
  animateCardImage(delay = 0) {
    this.scene.time.delayedCall(20, () => {
      const targetScale = this.cardImage.getData("targetScale") || 1;
      this.scene.tweens.add({
        targets: this.cardImage,
        scaleX: targetScale,
        scaleY: targetScale,
        duration: 300,
        ease: "Sine.easeInOut",
        delay
      });
    });
  }
  /**
   * Mark this card as the correct answer
   */
  setCorrect(isCorrect) {
    this.isCorrect = isCorrect;
  }
  /**
   * Check if this card is the correct answer
   */
  getIsCorrect() {
    return this.isCorrect;
  }
  /**
   * Mark the card as selected (right or wrong)
   */
  markSelected(isRight) {
    if (this.isSelected) return;
    this.isSelected = true;
    if (isRight) {
      this.cardBackground.setTexture("card_correct_bg");
      this.cardBackground.displayWidth = this.cardSize;
      this.cardBackground.displayHeight = this.cardSize;
      this.cardImage.setTint(65280);
    } else {
      this.cardBackground.setTexture("card_incorrect_bg");
      this.cardBackground.displayWidth = this.cardSize;
      this.cardBackground.displayHeight = this.cardSize;
      this.cardImage.setTint(16711680);
      this.scene.tweens.add({
        targets: this,
        x: this.x + 10,
        duration: 40,
        yoyo: true,
        repeat: 5
      });
    }
  }
  /**
   * Reset the card's selection state
   */
  resetSelection() {
    this.isSelected = false;
    this.cardBackground.setTexture("card_bg");
    this.cardBackground.displayWidth = this.cardSize;
    this.cardBackground.displayHeight = this.cardSize;
  }
  /**
   * Handle pointer over event
   */
  onPointerOver() {
    if (this.isSelected) return;
    this.scene.tweens.add({
      targets: this,
      scaleX: 1.05,
      scaleY: 1.05,
      duration: 100,
      ease: "Sine.easeOut"
    });
  }
  /**
   * Handle pointer out event
   */
  onPointerOut() {
    if (this.isSelected) return;
    this.scene.tweens.add({
      targets: this,
      scaleX: 1,
      scaleY: 1,
      duration: 100,
      ease: "Sine.easeOut"
    });
  }
  /**
   * Clean up resources when destroying the card
   */
  destroy(fromScene) {
    super.destroy(fromScene);
  }
  /**
   * Get the card background image
   */
  getCardBackground() {
    return this.cardBackground;
  }
}
let ScoreManager$1 = class ScoreManager3 {
  scene;
  config;
  score;
  // UI Elements
  scoreText;
  scoreLabel;
  container;
  // Events
  events;
  constructor(scene, config = {}) {
    this.scene = scene;
    this.events = new Phaser$1.Events.EventEmitter();
    this.config = {
      initialScore: config.initialScore ?? 0,
      fontFamily: config.fontFamily ?? "Arial",
      fontSize: config.fontSize ?? "80px",
      labelFontSize: config.labelFontSize ?? "28px",
      scoreColor: config.scoreColor ?? "#33DDFF",
      labelColor: config.labelColor ?? "#FFFFFF",
      animationColor: config.animationColor ?? "#ffff00",
      animationDuration: config.animationDuration ?? 800
    };
    this.score = this.config.initialScore;
  }
  /**
   * Create the score UI elements at the specified position
   */
  createUI(x, y, parentContainer) {
    this.container = this.scene.add.container(0, 0);
    this.scoreLabel = this.scene.add.text(x, y - 30, "Total Point", {
      fontFamily: this.config.fontFamily,
      fontSize: this.config.labelFontSize,
      fontStyle: "bold",
      color: this.config.labelColor
    }).setOrigin(0.5);
    this.scoreText = this.scene.add.text(x, y + 30, this.score.toString(), {
      fontFamily: this.config.fontFamily,
      fontSize: this.config.fontSize,
      fontStyle: "bold",
      color: this.config.scoreColor
    }).setOrigin(0.5);
    this.container.add([this.scoreLabel, this.scoreText]);
    if (parentContainer) {
      parentContainer.add(this.container);
    }
  }
  /**
   * Add points to the score with optional animation
   */
  addPoints(points, animationConfig) {
    this.score += points;
    this.updateScoreDisplay();
    if (animationConfig) {
      this.createScoreAnimation(animationConfig);
    }
    this.events.emit("scoreChanged", this.score, points);
  }
  /**
   * Subtract points from the score with optional animation
   */
  subtractPoints(points, animationConfig) {
    this.score = Math.max(0, this.score - points);
    this.updateScoreDisplay();
    this.events.emit("scoreChanged", this.score, -points);
  }
  /**
   * Set the score to a specific value
   */
  setScore(newScore) {
    const oldScore = this.score;
    this.score = newScore;
    this.updateScoreDisplay();
    this.events.emit("scoreChanged", this.score, this.score - oldScore);
  }
  /**
   * Get the current score
   */
  getScore() {
    return this.score;
  }
  /**
   * Reset score to initial value
   */
  reset() {
    this.score = this.config.initialScore;
    this.updateScoreDisplay();
    this.events.emit("scoreReset", this.score);
  }
  /**
   * Update the score display text
   */
  updateScoreDisplay() {
    if (this.scoreText) {
      this.scoreText.setText(this.score.toString());
    }
  }
  /**
   * Create animated flying score text
   */
  createScoreAnimation(config) {
    const animationText = this.scene.add.text(
      config.startX,
      config.startY,
      `+${config.points}`,
      {
        fontFamily: this.config.fontFamily,
        fontSize: "24px",
        color: config.color ?? this.config.animationColor,
        stroke: "#000000",
        strokeThickness: 3
      }
    );
    animationText.setOrigin(0.5);
    this.scene.tweens.add({
      targets: animationText,
      y: config.startY - 50,
      alpha: 0,
      scale: 1.2,
      duration: config.duration ?? this.config.animationDuration,
      ease: "Power2",
      onComplete: () => {
        animationText.destroy();
      }
    });
  }
  /**
   * Subscribe to score events
   */
  on(event, callback) {
    this.events.on(event, callback);
  }
  /**
   * Unsubscribe from score events
   */
  off(event, callback) {
    this.events.off(event, callback);
  }
  /**
   * Clean up resources
   */
  destroy() {
    this.events.removeAllListeners();
    if (this.container) {
      this.container.destroy();
    }
    this.scoreText = void 0;
    this.scoreLabel = void 0;
    this.container = void 0;
  }
};
let TimerManager$1 = class TimerManager2 {
  scene;
  config;
  // Timer state
  startTime = 0;
  isRunning = false;
  isFinished = false;
  // UI Elements
  timeText;
  container;
  // Timer event
  timerEvent;
  // Events
  events;
  constructor(scene, config) {
    this.scene = scene;
    this.events = new Phaser$1.Events.EventEmitter();
    this.config = {
      duration: config.duration,
      updateInterval: config.updateInterval ?? 100,
      fontFamily: config.fontFamily ?? "Arial",
      fontSize: config.fontSize ?? "24px",
      normalColor: config.normalColor ?? "#FFFFFF",
      warningColor: config.warningColor ?? "#ff0000",
      warningThreshold: config.warningThreshold ?? 5
    };
  }
  /**
   * Create the timer UI at the specified position
   */
  createUI(x, y, parentContainer) {
    this.container = this.scene.add.container(0, 0);
    this.timeText = this.scene.add.text(x, y, this.formatTime(this.config.duration), {
      fontFamily: this.config.fontFamily,
      fontSize: this.config.fontSize,
      fontStyle: "bold",
      color: this.config.normalColor
    }).setOrigin(0.5);
    this.container.add(this.timeText);
    if (parentContainer) {
      parentContainer.add(this.container);
    }
  }
  /**
   * Start the countdown timer
   */
  start() {
    if (this.isRunning) return;
    this.startTime = this.scene.time.now;
    this.isFinished = false;
    this.isRunning = true;
    if (this.timerEvent) {
      this.timerEvent.destroy();
    }
    this.timerEvent = this.scene.time.addEvent({
      delay: this.config.updateInterval,
      callback: this.updateTimer.bind(this),
      loop: true
    });
    this.events.emit("timerStarted");
  }
  /**
   * Stop and reset the timer
   */
  stop() {
    if (this.timerEvent) {
      this.timerEvent.destroy();
      this.timerEvent = void 0;
    }
    this.events.emit("timerStopped");
  }
  /**
   * Get current timer state
   */
  getState() {
    const timeRemaining = this.getTimeRemaining();
    const progress = timeRemaining / this.config.duration;
    return {
      timeRemaining,
      totalDuration: this.config.duration,
      isRunning: this.isRunning,
      isFinished: this.isFinished,
      progress: Math.max(0, Math.min(1, progress))
    };
  }
  /**
   * Get time remaining in seconds
   */
  getTimeRemaining() {
    if (!this.isRunning) return this.config.duration;
    const elapsed = this.scene.time.now - this.startTime;
    return Math.max(0, this.config.duration - elapsed / 1e3);
  }
  /**
   * Update timer display and check for completion
   */
  updateTimer() {
    const timeRemaining = this.getTimeRemaining();
    const seconds = Math.ceil(timeRemaining);
    if (this.timeText) {
      this.timeText.setText(this.formatTime(seconds));
      const color = seconds <= this.config.warningThreshold ? this.config.warningColor : this.config.normalColor;
      this.timeText.setColor(color);
    }
    const state = this.getState();
    this.events.emit("timerUpdate", state);
    if (timeRemaining <= 0 && !this.isFinished) {
      this.isFinished = true;
      this.isRunning = false;
      if (this.timerEvent) {
        this.timerEvent.destroy();
        this.timerEvent = void 0;
      }
      this.events.emit("timeUp");
    }
  }
  /**
   * Format time in seconds to display string
   */
  formatTime(seconds) {
    return `${Math.max(0, Math.floor(seconds))}s`;
  }
  /**
   * Subscribe to timer events
   */
  on(event, callback) {
    this.events.on(event, callback);
  }
  /**
   * Unsubscribe from timer events
   */
  off(event, callback) {
    this.events.off(event, callback);
  }
  /**
   * Clean up resources
   */
  destroy() {
    this.stop();
    this.events.removeAllListeners();
    if (this.container) {
      this.container.destroy();
    }
    this.timeText = void 0;
    this.container = void 0;
  }
};
let LivesManager$1 = class LivesManager2 {
  scene;
  config;
  lives;
  // UI Elements
  hearts = [];
  container;
  // Events
  events;
  constructor(scene, config = {}) {
    this.scene = scene;
    this.events = new Phaser$1.Events.EventEmitter();
    this.config = {
      initialLives: config.initialLives ?? 3
    };
    this.lives = this.config.initialLives;
  }
  /**
   * Create the lives UI elements at the specified position
   */
  createUI(x, y, parentContainer) {
    this.container = this.scene.add.container(x, y);
    const totalWidth = (this.lives - 1) * 40;
    const startX = -totalWidth / 2;
    for (let i = 0; i < this.lives; i++) {
      let heart = this.scene.add.image(
        startX + i * 40,
        0,
        "heart"
      ).setOrigin(0.5).setScale(1.5);
      this.hearts.push(heart);
    }
    this.container.add(this.hearts);
    if (parentContainer) {
      parentContainer.add(this.container);
    }
  }
  deductHeart(fromX, fromY) {
    this.lives--;
    if (this.hearts[this.lives]) {
      this.hearts[this.lives].setTexture("heart_outline");
    }
    if (fromX !== void 0 && fromY !== void 0 && this.container) {
      this.createFlyingHeartAnimation(fromX, fromY);
    }
    this.events.emit("heartDeducted", this.lives);
  }
  /**
   * Create animated flying heart_broken image
   */
  createFlyingHeartAnimation(startX, startY) {
    if (!this.container) return;
    const flyingHeart = this.scene.add.image(startX, startY, "heart_broken").setOrigin(0.5).setScale(1.5).setAlpha(0.4);
    this.scene.tweens.add({
      targets: flyingHeart,
      y: startY - 200,
      scale: 3,
      alpha: 0.8,
      duration: 600,
      ease: "Power2",
      onComplete: () => {
        flyingHeart.destroy();
      }
    });
  }
  /**
   * Subscribe to score events
   */
  on(event, callback) {
    this.events.on(event, callback);
  }
  /**
   * Unsubscribe from score events
   */
  off(event, callback) {
    this.events.off(event, callback);
  }
  /**
   * Clean up resources
   */
  destroy() {
    this.events.removeAllListeners();
    if (this.container) {
      this.container.destroy();
    }
    this.container = void 0;
  }
};
let TimerBarUI$1 = class TimerBarUI2 {
  scene;
  config;
  // UI Elements
  container;
  timerBar;
  timerMask;
  timerIcon;
  leftCircle;
  rightCircle;
  background;
  // Gradient texture name
  gradientTextureName;
  constructor(scene, config) {
    this.scene = scene;
    this.gradientTextureName = `timerBarGradient_${Date.now()}_${Math.random()}`;
    this.config = {
      width: config.width,
      height: config.height,
      x: config.x,
      y: config.y,
      backgroundColor: config.backgroundColor ?? 1118481,
      gradientStartColor: config.gradientStartColor ?? "#33DDFF",
      gradientEndColor: config.gradientEndColor ?? "#664DFF",
      cornerRadius: config.cornerRadius ?? 10,
      showTimerIcon: config.showTimerIcon ?? true,
      showCircularBg: config.showCircularBg ?? true,
      circleRadius: config.circleRadius ?? 30,
      circleBorderColor: config.circleBorderColor ?? 3399167,
      circleBorderWidth: config.circleBorderWidth ?? 2
    };
  }
  /**
   * Create the timer bar UI elements
   */
  create(parentContainer) {
    this.container = this.scene.add.container(0, 0);
    this.background = this.scene.add.rectangle(
      this.config.x,
      this.config.y,
      this.config.width,
      this.config.height,
      this.config.backgroundColor
    ).setOrigin(0.5);
    this.container.add(this.background);
    this.createGradientTexture();
    this.timerBar = this.scene.add.image(this.config.x, this.config.y, this.gradientTextureName).setOrigin(0.5);
    this.container.add(this.timerBar);
    this.createTimerMask();
    if (this.config.showCircularBg) {
      this.createCircularBackgrounds();
    }
    if (this.config.showTimerIcon) {
      this.createTimerIcon();
    }
    if (parentContainer) {
      parentContainer.add(this.container);
    }
  }
  /**
   * Update the progress of the timer bar (0-1)
   */
  updateProgress(progress) {
    if (!this.timerMask || !this.timerBar) return;
    progress = Math.max(0, Math.min(1, progress));
    const barWidth = this.config.width - 4;
    const barHeight = this.config.height - 4;
    const visibleWidth = barWidth * progress;
    this.timerMask.clear();
    if (visibleWidth > 0) {
      this.timerMask.fillStyle(16777215, 1);
      this.timerMask.fillRoundedRect(
        this.config.x - barWidth / 2,
        this.config.y - barHeight / 2,
        visibleWidth,
        barHeight,
        this.config.cornerRadius
      );
    }
  }
  /**
   * Create the gradient texture for the timer bar
   */
  createGradientTexture() {
    const barWidth = this.config.width - 4;
    const barHeight = this.config.height - 4;
    const barTexture = this.scene.textures.createCanvas(this.gradientTextureName, barWidth, barHeight);
    const barContext = barTexture?.getContext();
    if (barContext && barTexture) {
      const gradient = barContext.createLinearGradient(0, 0, barWidth, 0);
      gradient.addColorStop(0, this.config.gradientStartColor);
      gradient.addColorStop(1, this.config.gradientEndColor);
      barContext.fillStyle = gradient;
      barContext.fillRect(0, 0, barWidth, barHeight);
      barTexture.refresh();
    }
  }
  /**
   * Create the mask for the timer bar progress
   */
  createTimerMask() {
    if (!this.timerBar) return;
    const barWidth = this.config.width - 4;
    const barHeight = this.config.height - 4;
    this.timerMask = this.scene.make.graphics({});
    this.timerMask.fillRect(
      this.config.x - barWidth / 2,
      this.config.y - barHeight / 2,
      barWidth,
      barHeight
    );
    const mask = new Phaser$1.Display.Masks.GeometryMask(this.scene, this.timerMask);
    this.timerBar.setMask(mask);
  }
  /**
   * Create circular backgrounds on left and right
   */
  createCircularBackgrounds() {
    if (!this.container) return;
    const leftX = this.config.x - this.config.width / 2;
    const rightX = this.config.x + this.config.width / 2;
    this.leftCircle = this.scene.add.circle(
      leftX,
      this.config.y,
      this.config.circleRadius,
      2236962
    );
    this.leftCircle.setStrokeStyle(this.config.circleBorderWidth, this.config.circleBorderColor, 1);
    this.container.add(this.leftCircle);
    this.rightCircle = this.scene.add.circle(
      rightX,
      this.config.y,
      this.config.circleRadius,
      2236962
    );
    this.rightCircle.setStrokeStyle(this.config.circleBorderWidth, 6704639, 1);
    this.container.add(this.rightCircle);
  }
  /**
   * Create timer icon
   */
  createTimerIcon() {
    if (!this.container) return;
    const leftX = this.config.x - this.config.width / 2;
    if (this.scene.textures.exists("timer_icon")) {
      this.timerIcon = this.scene.add.image(leftX, this.config.y, "timer_icon").setOrigin(0.5).setScale(0.5);
    } else {
      const clockGraphics = this.scene.add.graphics();
      clockGraphics.lineStyle(2, 3399167);
      clockGraphics.strokeCircle(0, 0, 12);
      clockGraphics.moveTo(0, 0);
      clockGraphics.lineTo(0, -8);
      clockGraphics.moveTo(0, 0);
      clockGraphics.lineTo(6, 0);
      clockGraphics.setPosition(leftX, this.config.y);
      this.container.add(clockGraphics);
    }
    if (this.timerIcon) {
      this.container.add(this.timerIcon);
    }
  }
  /**
   * Get the container for additional positioning/effects
   */
  getContainer() {
    return this.container;
  }
  /**
   * Get the right circle position for timer text placement
   */
  getRightCirclePosition() {
    return {
      x: this.config.x + this.config.width / 2,
      y: this.config.y
    };
  }
  /**
   * Clean up resources
   */
  destroy() {
    if (this.scene.textures.exists(this.gradientTextureName)) {
      this.scene.textures.remove(this.gradientTextureName);
    }
    if (this.container) {
      this.container.destroy();
    }
    this.timerBar = void 0;
    this.timerMask = void 0;
    this.timerIcon = void 0;
    this.leftCircle = void 0;
    this.rightCircle = void 0;
    this.background = void 0;
    this.container = void 0;
  }
};
class RadialTimerUI {
  scene;
  config;
  // UI Elements
  container;
  backgroundGraphics;
  progressGraphics;
  maskGraphics;
  constructor(scene, config) {
    this.scene = scene;
    this.config = {
      x: config.x,
      y: config.y,
      size: config.size,
      cornerRadius: config.cornerRadius ?? 16,
      backgroundColor: config.backgroundColor ?? 3355443,
      backgroundAlpha: config.backgroundAlpha ?? 0.6,
      borderWidth: config.borderWidth ?? 8,
      startColor: config.startColor ?? "#ff4d4d",
      endColor: config.endColor ?? "#33ff55"
    };
  }
  /**
   * Create the radial timer UI elements
   */
  create(parentContainer) {
    this.container = this.scene.add.container(0, 0);
    this.createBackground();
    this.createProgress();
    if (parentContainer) {
      parentContainer.add(this.container);
    }
  }
  /**
   * Update the progress of the radial timer (0-1)
   */
  updateProgress(progress) {
    if (!this.progressGraphics) return;
    progress = Math.max(0, Math.min(1, progress));
    if (this.maskGraphics) {
      this.maskGraphics.destroy();
      this.maskGraphics = void 0;
    }
    this.progressGraphics.clearMask();
    this.progressGraphics.clear();
    if (progress <= 0) return;
    const color = Phaser$1.Display.Color.Interpolate.ColorWithColor(
      Phaser$1.Display.Color.HexStringToColor(this.config.startColor),
      Phaser$1.Display.Color.HexStringToColor(this.config.endColor),
      1,
      progress
    );
    const fillColor = Phaser$1.Display.Color.GetColor(color.r, color.g, color.b);
    this.maskGraphics = this.createRadialMask(progress);
    this.progressGraphics.lineStyle(this.config.borderWidth, fillColor, 1);
    this.progressGraphics.strokeRoundedRect(
      this.config.x - this.config.size / 2,
      this.config.y - this.config.size / 2,
      this.config.size,
      this.config.size,
      this.config.cornerRadius
    );
    if (this.maskGraphics) {
      const mask = new Phaser$1.Display.Masks.GeometryMask(this.scene, this.maskGraphics);
      this.progressGraphics.setMask(mask);
      this.maskGraphics.setVisible(false);
    }
  }
  /**
   * Create the background layer
   */
  createBackground() {
    if (!this.container) return;
    this.backgroundGraphics = this.scene.add.graphics();
    this.container.add(this.backgroundGraphics);
    this.backgroundGraphics.lineStyle(
      this.config.borderWidth,
      this.config.backgroundColor,
      this.config.backgroundAlpha
    );
    this.backgroundGraphics.strokeRoundedRect(
      this.config.x - this.config.size / 2,
      this.config.y - this.config.size / 2,
      this.config.size,
      this.config.size,
      this.config.cornerRadius
    );
  }
  /**
   * Create the progress layer
   */
  createProgress() {
    if (!this.container) return;
    this.progressGraphics = this.scene.add.graphics();
    this.container.add(this.progressGraphics);
  }
  /**
   * Create the radial mask for the sweep effect
   */
  createRadialMask(progress) {
    this.maskGraphics = this.scene.add.graphics();
    const startAngle = -Math.PI / 2;
    const sweepAngle = 2 * Math.PI * progress;
    const endAngle = startAngle + sweepAngle;
    this.maskGraphics.fillStyle(16777215, 1);
    this.maskGraphics.beginPath();
    this.maskGraphics.moveTo(this.config.x, this.config.y);
    const maskRadius = this.config.size * 0.8;
    this.maskGraphics.arc(this.config.x, this.config.y, maskRadius, startAngle, endAngle, false);
    this.maskGraphics.closePath();
    this.maskGraphics.fillPath();
    return this.maskGraphics;
  }
  /**
   * Set the depth of the timer UI
   */
  setDepth(depth) {
    if (this.container) {
      this.container.setDepth(depth);
    }
  }
  /**
   * Destroy the timer UI and clean up resources
   */
  destroy() {
    if (this.maskGraphics) {
      this.maskGraphics.destroy();
      this.maskGraphics = void 0;
    }
    if (this.progressGraphics) {
      this.progressGraphics.destroy();
      this.progressGraphics = void 0;
    }
    if (this.backgroundGraphics) {
      this.backgroundGraphics.destroy();
      this.backgroundGraphics = void 0;
    }
    if (this.container) {
      this.container.destroy();
      this.container = void 0;
    }
  }
  /**
   * Get the container for additional positioning/effects
   */
  getContainer() {
    return this.container;
  }
}
class SeededRandom2 {
  seed;
  a = 1664525;
  // Multiplier (from Numerical Recipes)
  c = 1013904223;
  // Increment
  m = 2 ** 32;
  // Modulus (2^32)
  constructor(seed = Date.now()) {
    this.seed = seed >>> 0;
  }
  /**
   * Generate next random number between 0 and 1 (exclusive)
   */
  next() {
    this.seed = (this.a * this.seed + this.c) % this.m;
    return this.seed / this.m;
  }
  /**
   * Generate random integer between min and max (inclusive)
   */
  between(min, max) {
    return Math.floor(this.next() * (max - min + 1)) + min;
  }
  /**
   * Shuffle an array in place using Fisher-Yates algorithm
   */
  shuffle(array) {
    for (let i = array.length - 1; i > 0; i--) {
      const j = this.between(0, i);
      [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
  }
  /**
   * Get current seed value
   */
  getSeed() {
    return this.seed;
  }
  /**
   * Reset with new seed
   */
  setSeed(newSeed) {
    this.seed = newSeed >>> 0;
  }
}
let GameScene$1 = class GameScene3 extends Phaser.Scene {
  // 2D array to store animal images by [animal][color]
  animalImages = [];
  // Game elements
  mainImage;
  optionCards = [];
  correctCardIndex = 0;
  // UI elements
  // private scoreText!: any; // Custom object with setText method
  // private timeText!: Phaser.GameObjects.Text | Phaser.GameObjects.BitmapText;
  radialTimerUI;
  bonusScoreText;
  UIContainer;
  scoreManager;
  timerManager;
  livesManager;
  timerBarUI;
  // UI panels
  countdownPanel;
  gamePanel;
  // Game state
  // private score: number = 0;
  isLocked = false;
  isGameOver = false;
  // private gameTime: number = GAME_CONFIG.GAME_TIME;
  roundTime = GAME_CONFIG.ROUND_TIME;
  currentRoundTime = 0;
  // private gameTimer!: Phaser.Time.TimerEvent;
  roundTimer;
  // Seeded random number generator
  rng;
  gameSeed;
  constructor(seed) {
    super("GameScene");
    this.rng = new SeededRandom2(Date.now());
    this.gameSeed = seed ?? Date.now();
    this.rng = new SeededRandom2(this.gameSeed);
    console.log(`Bingo Game initialized with seed: ${this.gameSeed}`);
  }
  init() {
    this.scoreManager = new ScoreManager$1(this, {
      initialScore: 0,
      fontSize: "80px",
      scoreColor: "#33DDFF"
    });
    this.timerManager = new TimerManager$1(this, {
      duration: GAME_CONFIG.GAME_TIME,
      warningThreshold: 5
    });
    this.livesManager = new LivesManager$1(this, {
      initialLives: 3
    });
    this.timerBarUI = new TimerBarUI$1(this, {
      width: this.cameras.main.width * 0.8,
      height: 35,
      x: this.cameras.main.width / 2,
      y: this.cameras.main.height * 0.07
    });
    this.timerManager.on("timeUp", () => this.endGame());
    this.timerManager.on("timerUpdate", (state) => {
      this.timerBarUI.updateProgress(state.progress);
    });
    this.livesManager.on("heartDeducted", (lives) => {
      if (lives === 0) {
        this.endGame();
      }
    });
  }
  create() {
    this.animalImages = this.game.registry.get("animalImages") || [];
    if (this.sys.game.renderer.type === Phaser.WEBGL) {
      this.lights.enable();
      this.lights.setAmbientColor(0);
    }
    this.cameras.main.setBackgroundColor(GAME_CONFIG.BACKGROUND_COLOR);
    const backgroundContainer = this.add.container(0, 0);
    backgroundContainer.setDepth(-10);
    const width = this.cameras.main.width;
    const height = this.cameras.main.height;
    const gradientTexture = this.textures.createCanvas("gradientBg", width, height);
    if (gradientTexture) {
      const context = gradientTexture.getContext();
      const gradient = context.createRadialGradient(
        width / 2,
        height / 2,
        0,
        // inner circle center and radius
        width / 2,
        height / 2,
        height * 0.8
        // outer circle center and radius
      );
      gradient.addColorStop(0, "#151B30");
      gradient.addColorStop(1, "#0E0F1E");
      context.fillStyle = gradient;
      context.fillRect(0, 0, width, height);
      for (let i = 0; i < 5e3; i++) {
        const x = this.rng.next() * width;
        const y = this.rng.next() * height;
        const size = this.rng.next() * 2;
        const alpha = this.rng.next() * 0.05;
        context.fillStyle = `rgba(255, 255, 255, ${alpha})`;
        context.fillRect(x, y, size, size);
      }
      gradientTexture.refresh();
      const gradientBg = this.add.image(0, 0, "gradientBg").setOrigin(0, 0);
      backgroundContainer.add(gradientBg);
      console.log("Created gradient background as fallback");
    } else {
      const baseRect = this.add.rectangle(
        0,
        0,
        this.cameras.main.width,
        this.cameras.main.height,
        921374
        // Dark blue color
      ).setOrigin(0, 0);
      backgroundContainer.add(baseRect);
    }
    const bgImage = this.add.image(0, 0, "game_background").setOrigin(0, 0);
    bgImage.displayWidth = this.cameras.main.width;
    bgImage.displayHeight = this.cameras.main.height;
    backgroundContainer.add(bgImage);
    this.gamePanel = this.add.container(0, 0);
    this.gamePanel.setVisible(false);
    this.gamePanel.setDepth(1);
    this.createCountdownPanel();
    this.createUI();
    this.startCountdown();
  }
  shutdown() {
    if (this.scoreManager) {
      this.scoreManager.destroy();
    }
    if (this.timerManager) {
      this.timerManager.destroy();
    }
    if (this.timerBarUI) {
      this.timerBarUI.destroy();
    }
    if (this.radialTimerUI) {
      this.radialTimerUI.destroy();
    }
    if (this.livesManager) {
      this.livesManager.destroy();
    }
  }
  /**
   * Create the countdown panel with countdown image
   */
  createCountdownPanel() {
    this.countdownPanel = this.add.container(0, 0);
    this.countdownPanel.setDepth(2);
    const overlay = this.add.rectangle(
      0,
      0,
      this.cameras.main.width,
      this.cameras.main.height,
      0,
      0.7
    ).setOrigin(0, 0);
    this.countdownPanel.add(overlay);
    const countdownImage = this.add.image(
      this.cameras.main.width / 2,
      this.cameras.main.height / 2,
      "countdown-3"
    ).setScale(0);
    this.countdownPanel.add(countdownImage);
    this.countdownPanel.setData("image", countdownImage);
  }
  /**
   * Start the game countdown sequence
   */
  async startCountdown() {
    const countdownImages = ["countdown-3", "countdown-2", "countdown-1", "countdown-go"];
    const countdownImage = this.countdownPanel.getData("image");
    for (let i = 0; i < countdownImages.length; i++) {
      await this.playCountdownStep(countdownImage, countdownImages[i], i === countdownImages.length - 1);
    }
    this.countdownPanel.visible = false;
    this.gamePanel.visible = true;
    console.log("Game panel visibility:", this.gamePanel.visible);
    console.log("Game started. Panel should be visible now.");
    this.startGameTimer();
    this.setupRound();
  }
  /**
   * Play a single step of the countdown animation
   */
  playCountdownStep(image, texture, isGo) {
    return new Promise((resolve) => {
      image.setTexture(texture);
      image.setScale(0);
      try {
        this.sound.play(isGo ? "go" : "countdown");
      } catch (error) {
        console.warn("Sound playback failed:", error);
      }
      this.tweens.add({
        targets: image,
        scale: 0.2,
        duration: 300,
        ease: "Back.easeOut",
        onComplete: () => {
          this.time.delayedCall(700, () => {
            this.tweens.add({
              targets: image,
              scale: 0,
              duration: 300,
              ease: "Back.easeIn",
              onComplete: () => resolve()
            });
          });
        }
      });
    });
  }
  createUI() {
    const { width, height } = this.cameras.main;
    this.UIContainer = this.add.container(0, 0);
    this.gamePanel.add(this.UIContainer);
    this.timerBarUI.create(this.UIContainer);
    const rightCirclePos = this.timerBarUI.getRightCirclePosition();
    this.timerManager.createUI(rightCirclePos.x, rightCirclePos.y, this.UIContainer);
    const timerBarY = height * 0.07;
    this.scoreManager.createUI(width / 2, timerBarY + 120, this.UIContainer);
    this.livesManager.createUI(width / 2, timerBarY + 50, this.UIContainer);
    this.createCenter();
    this.createRoundTimerUI();
  }
  createCenter() {
    const cardOffset = 110;
    const centerX = this.cameras.main.width / 2;
    const centerY = this.cameras.main.height * 0.55;
    const cardPositions = [
      { x: centerX - cardOffset, y: centerY - cardOffset },
      // Top left
      { x: centerX + cardOffset, y: centerY - cardOffset },
      // Top right
      { x: centerX, y: centerY },
      // Center
      { x: centerX - cardOffset, y: centerY + cardOffset },
      // Bottom left
      { x: centerX + cardOffset, y: centerY + cardOffset }
      // Bottom right
    ];
    for (let i = 0; i < GAME_CONFIG.OPTION_CARDS_COUNT; i++) {
      const card = new MatchingCard(
        this,
        cardPositions[i].x,
        cardPositions[i].y,
        GAME_CONFIG.CARD_SIZE
      );
      this.optionCards.push(card);
      if (i !== 2) {
        card.on("pointerdown", () => {
          this.checkAnswer(i);
        });
      }
    }
    for (let i = 0; i < GAME_CONFIG.OPTION_CARDS_COUNT; i++) {
      if (i !== 2) {
        this.gamePanel.add(this.optionCards[i]);
        this.optionCards[i].setDepth(1);
      }
    }
    const centerCard = this.optionCards[2];
    const solidBackdrop = this.add.graphics();
    solidBackdrop.fillStyle(1579032, 1);
    const backdropSize = GAME_CONFIG.CARD_SIZE * 0.9;
    solidBackdrop.fillRoundedRect(
      cardPositions[2].x - backdropSize / 2,
      cardPositions[2].y - backdropSize / 2,
      backdropSize,
      backdropSize,
      16
      // Corner radius
    );
    this.gamePanel.add(solidBackdrop);
    solidBackdrop.setDepth(8);
    const borderGlow = this.add.image(cardPositions[2].x, cardPositions[2].y, "card_bg").setOrigin(0.5).setDisplaySize(GAME_CONFIG.CARD_SIZE + 5, GAME_CONFIG.CARD_SIZE + 5).setTint(3399167).setAlpha(0.4);
    this.gamePanel.add(borderGlow);
    borderGlow.setDepth(9);
    this.gamePanel.add(centerCard);
    centerCard.setDepth(10);
    centerCard.getCardBackground().setAlpha(1);
    centerCard.disableInteractive();
  }
  createRoundTimerUI() {
    const { width, height } = this.cameras.main;
    this.radialTimerUI = new RadialTimerUI(this, {
      x: width / 2,
      y: height * 0.55,
      size: GAME_CONFIG.CARD_SIZE * 3,
      cornerRadius: 16,
      borderWidth: 8
    });
    this.radialTimerUI.create();
    this.radialTimerUI.setDepth(7);
    const container = this.radialTimerUI.getContainer();
    if (container) {
      this.gamePanel.add(container);
    }
    this.radialTimerUI.updateProgress(1);
  }
  updateRadialTimer(progress) {
    if (!this.radialTimerUI) return;
    this.radialTimerUI.updateProgress(progress);
  }
  startGameTimer() {
    this.timerManager.start();
  }
  setupRound() {
    this.sound.play("round");
    this.isLocked = false;
    this.currentRoundTime = this.roundTime;
    this.startRoundTimer();
    const correctAnimalIndex = this.rng.between(0, ANIMAL_NAMES.length - 1);
    const correctAnimal = ANIMAL_NAMES[correctAnimalIndex];
    const correctColorCategory = this.rng.between(0, COLOR_NAMES.length - 1);
    const correctColor = COLOR_NAMES[correctColorCategory];
    const mainImageKey = this.getAnimalImageKey(correctAnimalIndex, correctColorCategory);
    this.mainImage?.setTexture(mainImageKey);
    console.log(`Main image: ${correctAnimal} in ${correctColor} (indices: ${correctAnimalIndex}, ${correctColorCategory})`);
    const matchingImageCardIndex = 2;
    do {
      this.correctCardIndex = this.rng.between(0, 4);
    } while (this.correctCardIndex === 2);
    const availableAnimalIndices = Array.from(
      { length: ANIMAL_NAMES.length },
      (_, index) => index
    ).filter((index) => index !== correctAnimalIndex);
    this.rng.shuffle(availableAnimalIndices);
    const usedAnimalIndices = /* @__PURE__ */ new Set();
    usedAnimalIndices.add(correctAnimalIndex);
    for (let i = 0; i < this.optionCards.length; i++) {
      this.optionCards[i].resetSelection();
      if (i === this.correctCardIndex) {
        let cardColorCategory;
        do {
          cardColorCategory = this.rng.between(0, COLOR_NAMES.length - 1);
        } while (cardColorCategory === correctColorCategory);
        const cardColor = COLOR_NAMES[cardColorCategory];
        const cardImageKey = this.getAnimalImageKey(correctAnimalIndex, cardColorCategory);
        console.log(`Correct card (${i}): ${correctAnimal} in ${cardColor}`);
        this.optionCards[i].setCardImage(cardImageKey);
        this.optionCards[i].setCorrect(true);
        this.optionCards[i].setTint(CATEGORY_TINTS[cardColorCategory]);
      } else if (i === matchingImageCardIndex) {
        const matchingImageKey = this.getAnimalImageKey(correctAnimalIndex, correctColorCategory);
        console.log(`Matching distractor card (${i}): ${correctAnimal} in ${correctColor} (exact match to main)`);
        this.optionCards[i].setCardImage(matchingImageKey);
        this.optionCards[i].setCorrect(false);
        this.optionCards[i].setTint(CATEGORY_TINTS[correctColorCategory]);
      } else {
        const distractorAnimalIndex = availableAnimalIndices.length > 0 ? availableAnimalIndices.pop() : Array.from(usedAnimalIndices).find((index) => index !== correctAnimalIndex) || 0;
        if (!usedAnimalIndices.has(distractorAnimalIndex)) {
          usedAnimalIndices.add(distractorAnimalIndex);
        }
        const distractorAnimal = ANIMAL_NAMES[distractorAnimalIndex];
        const distractorColorCategory = this.rng.between(0, COLOR_NAMES.length - 1);
        const distractorColor = COLOR_NAMES[distractorColorCategory];
        const distractorImageKey = this.getAnimalImageKey(distractorAnimalIndex, distractorColorCategory);
        console.log(`Regular distractor card (${i}): ${distractorAnimal} in ${distractorColor}`);
        this.optionCards[i].setCardImage(distractorImageKey);
        this.optionCards[i].setCorrect(false);
        this.optionCards[i].setTint(CATEGORY_TINTS[distractorColorCategory]);
      }
      this.optionCards[i].animateCardImage(200 + i * 50);
    }
  }
  startRoundTimer() {
    if (this.roundTimer) {
      this.roundTimer.remove();
    }
    this.roundTimer = this.time.addEvent({
      delay: 10,
      callback: this.updateRoundTimer,
      callbackScope: this,
      repeat: this.roundTime * 100 - 1
    });
  }
  updateRoundTimer() {
    if (this.isLocked || this.isGameOver) return;
    this.currentRoundTime = Math.max(0, Math.round((this.currentRoundTime - 0.01) * 100) / 100);
    const percentage = this.currentRoundTime / this.roundTime;
    this.updateRadialTimer(percentage);
    if (this.currentRoundTime <= 0) {
      if (this.roundTimer) {
        this.roundTimer.remove();
      }
      this.time.delayedCall(500, this.setupRound, [], this);
    }
  }
  checkAnswer(index) {
    if (this.isLocked || this.isGameOver) return;
    this.isLocked = true;
    if (index === this.correctCardIndex) {
      this.sound.play("correct");
      this.optionCards[index].markSelected(true);
      const percentage = Math.floor(this.currentRoundTime / this.roundTime * 100);
      this.scoreManager.addPoints(percentage);
      this.showBonusText(`Good Choice +${percentage}`, true);
      if (this.roundTimer) {
        this.roundTimer.remove();
      }
      this.time.delayedCall(780, this.setupRound, [], this);
    } else {
      this.sound.play("wrong");
      this.optionCards[index].markSelected(false);
      const penalty = 20;
      this.scoreManager.subtractPoints(penalty);
      this.livesManager.deductHeart(
        this.cameras.main.width / 2,
        this.cameras.main.height / 2
      );
      this.showBonusText(`Bad Choice -${penalty}`, false);
      this.time.delayedCall(500, () => {
        this.isLocked = false;
      });
    }
  }
  showBonusText(text, isCorrect) {
    if (!this.bonusScoreText || !(this.bonusScoreText instanceof Phaser.GameObjects.Text)) {
      this.bonusScoreText = this.add.text(
        this.cameras.main.width / 2,
        this.cameras.main.height / 2.5,
        text,
        {
          fontFamily: "Arial",
          fontSize: "32px",
          fontStyle: "italic",
          color: isCorrect ? "#4FFFAA" : "#FF4F59",
          stroke: "#000000",
          strokeThickness: 3,
          shadow: { offsetX: 1, offsetY: 1, color: "#000000", blur: 2, stroke: true, fill: true }
        }
      ).setOrigin(0.5).setDepth(100).setAlpha(0);
      this.gamePanel.add(this.bonusScoreText);
    } else {
      this.bonusScoreText.setText(text);
      this.bonusScoreText.setColor(isCorrect ? "#4FFFAA" : "#FF4F59");
      this.bonusScoreText.setPosition(this.cameras.main.width / 2, this.cameras.main.height / 2.5);
    }
    const targetY = this.cameras.main.height / 2.5 - 50;
    this.tweens.killTweensOf(this.bonusScoreText);
    this.bonusScoreText.setAlpha(0);
    this.bonusScoreText.setPosition(this.cameras.main.width / 2, this.cameras.main.height / 2.5);
    this.tweens.add({
      targets: this.bonusScoreText,
      alpha: 1,
      duration: 200,
      ease: "Linear"
    });
    this.tweens.add({
      targets: this.bonusScoreText,
      y: targetY,
      duration: 700,
      ease: "Cubic.easeOut"
    });
    this.tweens.add({
      targets: this.bonusScoreText,
      alpha: 0,
      delay: 600,
      duration: 300,
      ease: "Linear"
    });
  }
  /**
   * Get the image key for a specific animal and color
   * @param animalIndex The index of the animal
   * @param colorIndex The index of the color
   * @returns The image key
   */
  getAnimalImageKey(animalIndex, colorIndex) {
    if (this.animalImages && animalIndex >= 0 && animalIndex < this.animalImages.length && colorIndex >= 0 && colorIndex < this.animalImages[animalIndex].length) {
      return this.animalImages[animalIndex][colorIndex];
    }
    console.warn(`Using fallback image key for animal ${animalIndex}, color ${colorIndex}`);
    return `image_${colorIndex}_${animalIndex}`;
  }
  endGame() {
    if (this.isGameOver) return;
    this.isGameOver = true;
    if (this.roundTimer) {
      this.roundTimer.remove();
    }
    this.sound.play("end");
    this.time.delayedCall(500, () => {
      this.scene.start("GameEndScene", { score: this.scoreManager.getScore() });
    });
  }
};
let GameEndScene$1 = class GameEndScene3 extends Phaser.Scene {
  ticTaps;
  score = 0;
  backToLobbyButton;
  constructor() {
    super("GameEndScene");
  }
  init(data) {
    this.score = data.score || 0;
  }
  create() {
    this.ticTaps = new TicTapsConnector$1();
    this.add.image(0, 0, "game_background").setOrigin(0, 0).setDisplaySize(this.cameras.main.width, this.cameras.main.height);
    const panelWidth = this.cameras.main.width * 0.8;
    const panelHeight = this.cameras.main.height * 0.6;
    if (this.sys.game.renderer.type === Phaser.WEBGL) {
      const blurBg = this.add.graphics();
      blurBg.fillStyle(0, 0.3);
      blurBg.fillRoundedRect(
        this.cameras.main.width / 2 - panelWidth / 2 - 2,
        this.cameras.main.height / 2 - panelHeight / 2 - 2,
        panelWidth + 4,
        panelHeight + 4,
        20
      );
      blurBg.postFX.addBlur(0, 0, 1, 2, 1, 1);
    }
    const panel = this.add.graphics();
    panel.fillStyle(1712945, 0.4);
    panel.fillRoundedRect(
      this.cameras.main.width / 2 - panelWidth / 2,
      this.cameras.main.height / 2 - panelHeight / 2,
      panelWidth,
      panelHeight,
      20
    );
    const gameOverImage = this.add.image(
      this.cameras.main.width / 2,
      this.cameras.main.height / 2 - panelHeight * 0.5,
      // Positioned at top of panel
      "game_over"
    ).setOrigin(0.5);
    const gameOverScale = panelWidth * 0.8 / gameOverImage.width;
    gameOverImage.setScale(gameOverScale);
    if (this.sys.game.renderer.type === Phaser.WEBGL) {
      gameOverImage.postFX.addGlow(4980654, 1, 0, false, 0.1, 15);
    }
    this.add.text(
      this.cameras.main.width / 2,
      this.cameras.main.height / 2 - 100,
      // Slight offset above center
      "SCORE",
      {
        fontFamily: "Arial",
        fontSize: "30px",
        fontStyle: "bold",
        color: "#FFFFFF"
      }
    ).setOrigin(0.5);
    this.createGradientText(
      this.cameras.main.width / 2,
      this.cameras.main.height / 2,
      // Below "SCORE" text
      this.score.toString(),
      90,
      true
      // Make it with gradient and white outline
    );
    this.createBackToLobbyButton();
  }
  /**
   * Create the Back to Lobby button with gradient border as in Image 2
   */
  createBackToLobbyButton() {
    const buttonWidth = this.cameras.main.width * 0.7;
    const buttonHeight = 80;
    const buttonX = this.cameras.main.width / 2;
    const buttonY = this.cameras.main.height / 2 + this.cameras.main.height * 0.2;
    this.backToLobbyButton = this.add.container(buttonX, buttonY);
    const borderCanvas = this.textures.createCanvas("buttonBorder", buttonWidth + 4, buttonHeight + 4);
    if (borderCanvas) {
      const borderContext = borderCanvas.getContext();
      const gradient = borderContext.createLinearGradient(0, 0, buttonWidth + 4, 0);
      gradient.addColorStop(0, "#32c4ff");
      gradient.addColorStop(0.5, "#7f54ff");
      gradient.addColorStop(1, "#b63efc");
      borderContext.strokeStyle = gradient;
      borderContext.lineWidth = 2.5;
      roundRect$1(borderContext, 2, 2, buttonWidth, buttonHeight, 18);
      borderCanvas.refresh();
      const border = this.add.image(0, 0, "buttonBorder").setOrigin(0.5);
      this.backToLobbyButton.add(border);
    }
    const buttonBg = this.add.graphics();
    buttonBg.fillStyle(1185311, 1);
    buttonBg.fillRoundedRect(-buttonWidth / 2 + 2, -buttonHeight / 2 + 2, buttonWidth - 4, buttonHeight - 4, 16);
    this.backToLobbyButton.add(buttonBg);
    if (this.textures.exists("back_to_lobby")) {
      const buttonText = this.add.image(0, 0, "back_to_lobby").setOrigin(0.5);
      const textScale = Math.min(buttonWidth * 0.7 / buttonText.width, buttonHeight * 0.6 / buttonText.height);
      buttonText.setScale(textScale);
      this.backToLobbyButton.add(buttonText);
    } else {
      const buttonText = this.createGradientText(0, 0, "BACK TO LOBBY", 28, false, true);
      this.backToLobbyButton.add(buttonText);
    }
    const hitArea = new Phaser.Geom.Rectangle(-buttonWidth / 2, -buttonHeight / 2, buttonWidth, buttonHeight);
    this.backToLobbyButton.setInteractive(hitArea, Phaser.Geom.Rectangle.Contains);
    this.backToLobbyButton.on("pointerover", () => {
      this.backToLobbyButton.setScale(1.05);
    });
    this.backToLobbyButton.on("pointerout", () => {
      this.backToLobbyButton.setScale(1);
    });
    this.backToLobbyButton.on("pointerdown", () => {
      if (this.sound.get("laser")) {
        this.sound.play("laser", { volume: 0.7 });
      } else if (this.sound.get("countdown")) {
        this.sound.play("countdown", { volume: 0.7 });
      }
      this.backToLobbyButton.setScale(0.95);
      this.time.delayedCall(100, () => {
        this.backToLobbyButton.setScale(1);
        this.endGame();
      });
    });
  }
  /**
   * Helper function to create gradient text
   */
  createGradientText(x, y, text, fontSize = 32, isScoreText = false, isButtonText = false) {
    const textureName = "gradientText-" + text.replace(/\s+/g, "-") + "-" + fontSize + (isScoreText ? "-score" : "") + (isButtonText ? "-button" : "");
    if (this.textures.exists(textureName)) {
      this.textures.remove(textureName);
    }
    const width = Math.max(400, text.length * fontSize * 0.7);
    const height = fontSize * 1.5;
    const textCanvas = this.textures.createCanvas(textureName, width, height);
    if (!textCanvas) {
      console.error("Failed to create gradient text canvas");
      return this.add.image(x, y, "").setOrigin(0.5);
    }
    const context = textCanvas.getContext();
    const gradient = context.createLinearGradient(0, 0, width, height * 0.5);
    if (isScoreText) {
      gradient.addColorStop(0, "#4cffae");
      gradient.addColorStop(0.4, "#32c4ff");
      gradient.addColorStop(1, "#5c67ff");
    } else if (isButtonText) {
      gradient.addColorStop(0, "#32c4ff");
      gradient.addColorStop(0.5, "#7f54ff");
      gradient.addColorStop(1, "#b63efc");
    } else {
      gradient.addColorStop(0, "#33DDFF");
      gradient.addColorStop(1, "#664DFF");
    }
    context.font = `bold ${fontSize}px Arial`;
    context.textAlign = "center";
    context.textBaseline = "middle";
    if (isScoreText) {
      context.strokeStyle = "rgba(255, 255, 255, 0.9)";
      context.lineWidth = 5;
      context.strokeText(text, width / 2, height / 2);
    }
    context.fillStyle = gradient;
    context.fillText(text, width / 2, height / 2);
    textCanvas.refresh();
    return this.add.image(x, y, textureName).setOrigin(0.5);
  }
  endGame() {
    const flash = this.add.rectangle(
      this.cameras.main.width / 2,
      this.cameras.main.height / 2,
      this.cameras.main.width,
      this.cameras.main.height,
      16777215
    ).setAlpha(0).setOrigin(0.5);
    flash.setDepth(1e3);
    this.ticTaps.sendScore(this.score);
    this.ticTaps.notifyGameQuit();
    this.tweens.add({
      targets: flash,
      alpha: 0.8,
      duration: 100,
      ease: "Sine.easeOut",
      onComplete: () => {
        this.tweens.add({
          targets: flash,
          alpha: 0,
          delay: 50,
          duration: 250,
          ease: "Sine.easeIn",
          onComplete: () => {
            this.scene.start("GameStartScene");
          }
        });
      }
    });
  }
};
function roundRect$1(ctx, x, y, width, height, radius, fill, stroke) {
  ctx.beginPath();
  ctx.moveTo(x + radius, y);
  ctx.lineTo(x + width - radius, y);
  ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
  ctx.lineTo(x + width, y + height - radius);
  ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
  ctx.lineTo(x + radius, y + height);
  ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
  ctx.lineTo(x, y + radius);
  ctx.quadraticCurveTo(x, y, x + radius, y);
  ctx.closePath();
  {
    ctx.stroke();
  }
}
class PreloadScene4 extends Phaser$1.Scene {
  constructor() {
    super("PreloadScene");
  }
  preload() {
    const { width, height } = this.cameras.main;
    const bgBar = this.add.rectangle(
      width / 2,
      height / 2,
      width / 2,
      20,
      2302755
    );
    const progressBar = this.add.rectangle(
      bgBar.x - bgBar.width / 2,
      bgBar.y,
      0,
      bgBar.height,
      65280
    );
    progressBar.setOrigin(0, 0.5);
    const loadingText = this.add.text(
      width / 2,
      height / 2 - 30,
      "Loading...",
      {
        font: "24px Arial",
        color: "#ffffff"
      }
    ).setOrigin(0.5);
    this.load.on("progress", (value) => {
      progressBar.width = bgBar.width * value;
    });
    this.load.on("complete", () => {
      progressBar.destroy();
      bgBar.destroy();
      loadingText.destroy();
    });
    this.load.image("countdown-1", "/assets/images/countdown-1.png");
    this.load.image("countdown-2", "/assets/images/countdown-2.png");
    this.load.image("countdown-3", "/assets/images/countdown-3.png");
    this.load.image("countdown-go", "/assets/images/countdown-go.png");
    this.load.svg("heart", "/assets/images/mdi--heart.svg");
    this.load.svg("heart_outline", "/assets/images/mdi-light--heart.svg");
    this.load.svg("heart_broken", "/assets/images/mdi--heart-broken.svg");
    this.load.image("game_name", "/assets-numbers/images/game_name.svg");
    this.load.image("button_bg", "/assets/images/button_bg.svg");
    this.load.image("game_start", "/assets/images/game_start.png");
    this.load.image("game_background", "/assets/images/game_bg.png");
    this.load.image("game_over", "/assets/images/game_over.svg");
    this.load.image("back_to_lobby", "/assets/images/back_to_lobby.png");
    this.load.image("timer_bg", "/assets/images/timer_bg.svg");
    this.load.image("timer_icon", "/assets/images/timer_icon.png");
    this.load.image("timer_countdown_bg", "/assets/images/timer_countdown_bg.png");
    this.load.image("circle", "/assets-numbers/images/circle.png");
    this.load.audio("countdown", [
      "/assets/audio/countdown.ogg",
      "/assets/audio/countdown.mp3"
    ]);
    this.load.audio("click", [
      "/assets/audio/click.ogg",
      "/assets/audio/click.mp3"
    ]);
    this.load.audio("go", [
      "/assets/sounds/go.ogg",
      "/assets/sounds/go.mp3"
    ]);
    this.load.audio("collect", [
      "/assets-numbers/sounds/collect.ogg",
      "/assets-numbers/sounds/collect.mp3"
    ]);
    this.load.audio("complete", [
      "/assets-numbers/sounds/complete.ogg",
      "/assets-numbers/sounds/complete.mp3"
    ]);
    this.load.audio("error", [
      "/assets-numbers/sounds/error.ogg",
      "/assets-numbers/sounds/error.mp3"
    ]);
    this.load.audio("timeout", [
      "/assets-numbers/sounds/timeout.ogg",
      "/assets-numbers/sounds/timeout.mp3"
    ]);
  }
  create() {
    this.scene.start("GameStartScene");
  }
}
class TicTapsConnector4 {
  isWebGL;
  constructor() {
    this.isWebGL = this.checkIfWebGL();
  }
  /**
   * Check if running in a browser environment and embedded
   */
  checkIfWebGL() {
    return typeof window !== "undefined" && window.parent && window.parent !== window;
  }
  /**
   * Notify parent window that the game is ready
   * Equivalent to TicTaps.Instance.NotifyGameReady()
   */
  notifyGameReady() {
    this.sendMessage({ type: "gameReady" });
  }
  /**
   * Send score to parent window
   * Equivalent to TicTaps.Instance.SendScore()
   */
  sendScore(score) {
    this.sendMessage({ type: "gameScore", score });
  }
  /**
   * Notify parent window that the game has quit
   * Equivalent to TicTaps.Instance.NotifyGameQuit()
   */
  notifyGameQuit() {
    this.sendMessage({ type: "gameQuit" });
  }
  /**
  * Send a typed message to the parent window
  */
  sendMessage(message) {
    if (this.isWebGL && window.parent && typeof window.parent.postMessage === "function") {
      window.parent.postMessage(message, "*");
      console.log("Message sent to parent:", message);
    }
  }
}
class GameConfig2 {
  static GAME_DURATION = 30;
  // in seconds
  static SOUND_VOLUME = 0.7;
  static CIRCLE_RADIUS = 38.5;
  // 35 * 1.1 = 38.5 (10% larger)
  // Animation settings
  static FLASH_DURATION = 100;
  // milliseconds
  static TRANSITION_DELAY = 50;
  // milliseconds
  static TRANSITION_FADE_DURATION = 250;
  // milliseconds
}
class GameStartScene4 extends Phaser.Scene {
  ticTaps;
  startButton;
  isStarting = false;
  constructor() {
    super("GameStartScene");
  }
  create() {
    const { width, height } = this.cameras.main;
    this.ticTaps = new TicTapsConnector4();
    if (this.textures.exists("game_background")) {
      this.add.image(0, 0, "game_background").setOrigin(0, 0).setDisplaySize(width, height);
    } else {
      this.cameras.main.setBackgroundColor("#000000");
      console.warn("game_background asset missing, using fallback");
    }
    let gameTitle;
    let titleScale;
    if (this.textures.exists("game_name")) {
      gameTitle = this.add.image(
        width / 2,
        height * 0.25,
        "game_name"
      ).setOrigin(0.5);
      titleScale = // (width * 0.6) / (gameTitle as Phaser.GameObjects.Image).width;
      Math.min(width * 0.7 / gameTitle.width, 0.8);
      gameTitle.setScale(titleScale);
    } else {
      gameTitle = this.add.text(
        width / 2,
        height * 0.25,
        "NUMBERS GAME",
        {
          fontFamily: "NeorisTrialBold, Arial",
          fontSize: "48px",
          fontStyle: "bold",
          color: "#ffffff"
        }
      ).setOrigin(0.5);
      titleScale = 1;
      console.warn("game_name asset missing, using fallback text");
    }
    this.tweens.add({
      targets: gameTitle,
      scaleX: titleScale * 1.02,
      scaleY: titleScale * 1.02,
      duration: 1500,
      yoyo: true,
      repeat: -1,
      ease: "Sine.easeInOut"
    });
    let buttonScale;
    let startText;
    if (this.textures.exists("button_bg")) {
      this.startButton = this.add.image(
        width / 2,
        height * 0.6,
        "button_bg"
      ).setOrigin(0.5);
      buttonScale = //(width * 0.6) / this.startButton.width;
      Math.min(width * 0.6 / this.startButton.width, 0.4);
      this.startButton.setScale(buttonScale);
      if (this.textures.exists("game_start")) {
        startText = this.add.image(
          this.startButton.x,
          this.startButton.y - 5,
          "game_start"
        ).setOrigin(0.5);
        const textScale = this.startButton.displayWidth * 0.6 / startText.width;
        startText.setScale(textScale);
      } else {
        startText = this.add.text(
          this.startButton.x,
          this.startButton.y,
          "START",
          {
            fontFamily: "NeorisTrialBold, Arial",
            fontSize: "32px",
            fontStyle: "bold",
            color: "#ffffff"
          }
        ).setOrigin(0.5);
        console.warn("game_start asset missing, using fallback text");
      }
    } else {
      const buttonBg = this.add.graphics();
      buttonBg.fillStyle(31436, 1);
      buttonBg.lineStyle(2, 52479, 1);
      buttonBg.fillRoundedRect(-120, -40, 240, 80, 20);
      buttonBg.strokeRoundedRect(-120, -40, 240, 80, 20);
      this.startButton = this.add.container(
        width / 2,
        height * 0.6,
        [buttonBg]
      );
      buttonScale = 1;
      startText = this.add.text(
        this.startButton.x,
        this.startButton.y,
        "START GAME",
        {
          fontFamily: "NeorisTrialBold, Arial",
          fontSize: "32px",
          fontStyle: "bold",
          color: "#ffffff"
        }
      ).setOrigin(0.5);
      console.warn("button_bg asset missing, using fallback button");
    }
    this.startButton.setInteractive({ useHandCursor: true });
    this.startButton.on("pointerover", () => {
      this.tweens.add({
        targets: [this.startButton, startText],
        scaleX: buttonScale * 1.05,
        scaleY: buttonScale * 1.05,
        duration: 150,
        ease: "Sine.easeOut"
      });
    });
    this.startButton.on("pointerout", () => {
      this.tweens.add({
        targets: [this.startButton, startText],
        scaleX: buttonScale,
        scaleY: buttonScale,
        duration: 150,
        ease: "Sine.easeOut"
      });
    });
    this.startButton.on("pointerdown", () => {
      try {
        if (this.sound.get("countdown")) {
          this.sound.play("countdown", { volume: GameConfig2.SOUND_VOLUME });
        }
      } catch (error) {
        console.warn("Sound playback failed:", error);
      }
      this.tweens.add({
        targets: [this.startButton, startText],
        scaleX: buttonScale * 0.95,
        scaleY: buttonScale * 0.95,
        duration: GameConfig2.FLASH_DURATION,
        yoyo: true,
        onComplete: () => this.startGameCountdown(width, height)
      });
    });
  }
  startGameCountdown(width, height) {
    if (this.isStarting) return;
    this.isStarting = true;
    const flash = this.add.rectangle(
      width / 2,
      height / 2,
      width,
      height,
      16777215
    ).setAlpha(0).setOrigin(0.5);
    flash.setDepth(1e3);
    this.tweens.add({
      targets: flash,
      alpha: 0.8,
      duration: GameConfig2.FLASH_DURATION,
      ease: "Sine.easeOut",
      onComplete: () => {
        if (this.sound.get("go")) {
          this.sound.play("go", { volume: GameConfig2.SOUND_VOLUME });
        }
        this.tweens.add({
          targets: flash,
          alpha: 0,
          delay: GameConfig2.TRANSITION_DELAY,
          duration: GameConfig2.TRANSITION_FADE_DURATION,
          ease: "Sine.easeIn",
          onComplete: () => {
            this.ticTaps.notifyGameReady();
            this.scene.start("GameScene");
          }
        });
      }
    });
  }
}
class NumberObject extends Phaser$1.GameObjects.Container {
  number;
  circle;
  text;
  glowEffect;
  innerStroke;
  originalX;
  originalY;
  radius;
  objectState = "active";
  constructor(scene, x, y, number) {
    super(scene, x, y);
    this.number = number;
    this.originalX = x;
    this.originalY = y;
    this.radius = GameConfig2.CIRCLE_RADIUS;
    this.glowEffect = scene.add.graphics();
    this.createGlowEffect();
    this.add(this.glowEffect);
    this.circle = scene.add.sprite(0, 0, "circle");
    this.circle.setScale(this.radius * 2 / 160);
    this.circle.setInteractive();
    this.circle.on("pointerdown", () => {
      this.onPointerDown();
    });
    this.add(this.circle);
    this.createInnerStroke();
    this.text = scene.add.text(0, 0, (number + 1).toString(), {
      fontSize: "46px",
      // Increased proportionally (42 * 1.1 = 46.2)
      fontFamily: "Arial",
      color: "#ffffff",
      stroke: "#003366",
      strokeThickness: 2
    });
    this.text.setOrigin(0.5);
    this.add(this.text);
    this.scene.add.existing(this);
    this.setSize(this.radius * 2, this.radius * 2);
    this.setInteractive(new Phaser$1.Geom.Circle(0, 0, this.radius), Phaser$1.Geom.Circle.Contains);
    this.on("pointerdown", () => {
      this.onPointerDown();
    });
    this.playSpawnAnimation();
  }
  get numberValue() {
    return this.number;
  }
  get currentState() {
    return this.objectState;
  }
  setObjectState(newState) {
    this.objectState = newState;
  }
  // No need for createCircle method as we're using an SVG image
  createGlowEffect() {
    this.glowEffect.clear();
    for (let i = this.radius + 15; i >= this.radius; i -= 2) {
      const opacity = (this.radius + 16 - i) / 16;
      this.glowEffect.lineStyle(3, 48127, opacity * 0.6);
      this.glowEffect.strokeCircle(0, 0, i);
    }
    this.glowEffect.fillStyle(13158, 0.4);
    this.glowEffect.fillCircle(0, 0, this.radius);
  }
  setActive(active) {
    if (active) {
      this.glowEffect.clear();
      this.glowEffect.fillStyle(35071, 0.9);
      this.glowEffect.fillCircle(0, 0, this.radius);
      this.glowEffect.lineStyle(4, 65535, 1);
      this.glowEffect.strokeCircle(0, 0, this.radius);
      this.circle.setAlpha(1);
      this.setObjectState("active");
    } else {
      this.createGlowEffect();
      this.circle.setAlpha(0.9);
      this.setObjectState("inactive");
    }
    return this;
  }
  createInnerStroke() {
    this.innerStroke = this.scene.add.graphics();
    this.innerStroke.lineStyle(2, 16777215, 0.8);
    this.innerStroke.strokeCircle(0, 0, this.radius - 3);
    this.add(this.innerStroke);
  }
  setError() {
    this.setObjectState("error");
    this.glowEffect.clear();
    this.glowEffect.fillStyle(16711680, 0.7);
    this.glowEffect.fillCircle(0, 0, this.radius);
    this.glowEffect.lineStyle(4, 16711680, 1);
    this.glowEffect.strokeCircle(0, 0, this.radius);
    this.circle.setTint(16737894);
    this.scene.tweens.add({
      targets: this,
      x: this.x + 3,
      duration: 50,
      //MORE TIME PUNISHED FOR WRONG SELECTION
      yoyo: true,
      repeat: 5,
      ease: "Power1",
      onComplete: () => {
        this.x = this.originalX;
        this.y = this.originalY;
        this.clearError();
      }
    });
  }
  clearError() {
    this.createGlowEffect();
    this.circle.clearTint();
    this.setObjectState("active");
  }
  reset() {
    this.setActive(false);
    this.clearError();
    this.setVisible(true);
    this.alpha = 1;
  }
  onPointerDown() {
    const gameScene = this.scene;
    if (gameScene.handleNumberClick) {
      gameScene.handleNumberClick(this);
    }
  }
  startRotation() {
    this.scene.tweens.add({
      targets: this,
      angle: -360,
      duration: 2e3,
      repeat: -1,
      ease: "Linear"
    });
  }
  stopRotation() {
    this.scene.tweens.killTweensOf(this);
    this.angle = 0;
  }
  playSpawnAnimation() {
    this.setScale(0);
    this.scene.tweens.add({
      targets: this,
      scaleX: 1.1,
      scaleY: 1.1,
      duration: 250,
      // Half the total duration to reach 110%
      ease: "Back.easeOut",
      onComplete: () => {
        this.scene.tweens.add({
          targets: this,
          scaleX: 1,
          scaleY: 1,
          duration: 250,
          // Remaining duration to reach 100%
          ease: "Back.easeIn"
        });
      }
    });
  }
  playDisappearAnimation(onComplete) {
    this.scene.tweens.add({
      targets: this,
      scaleX: 0,
      scaleY: 0,
      duration: 300,
      // Quick disappear animation
      ease: "Back.easeIn",
      onComplete: () => {
        this.setVisible(false);
        onComplete();
      }
    });
  }
}
class ScoreManager4 {
  scene;
  config;
  score;
  // UI Elements
  scoreText;
  scoreLabel;
  container;
  // Events
  events;
  constructor(scene, config = {}) {
    this.scene = scene;
    this.events = new Phaser$1.Events.EventEmitter();
    this.config = {
      initialScore: config.initialScore ?? 0,
      fontFamily: config.fontFamily ?? "Arial",
      fontSize: config.fontSize ?? "80px",
      labelFontSize: config.labelFontSize ?? "28px",
      scoreColor: config.scoreColor ?? "#33DDFF",
      labelColor: config.labelColor ?? "#FFFFFF",
      animationColor: config.animationColor ?? "#ffff00",
      animationDuration: config.animationDuration ?? 800
    };
    this.score = this.config.initialScore;
  }
  /**
   * Create the score UI elements at the specified position
   */
  createUI(x, y, parentContainer) {
    this.container = this.scene.add.container(0, 0);
    this.scoreLabel = this.scene.add.text(x, y - 30, "Total Point", {
      fontFamily: this.config.fontFamily,
      fontSize: this.config.labelFontSize,
      fontStyle: "bold",
      color: this.config.labelColor
    }).setOrigin(0.5);
    this.scoreText = this.scene.add.text(x, y + 30, this.score.toString(), {
      fontFamily: this.config.fontFamily,
      fontSize: this.config.fontSize,
      fontStyle: "bold",
      color: this.config.scoreColor
    }).setOrigin(0.5);
    this.container.add([this.scoreLabel, this.scoreText]);
    if (parentContainer) {
      parentContainer.add(this.container);
    }
  }
  /**
   * Add points to the score with optional animation
   */
  addPoints(points, animationConfig) {
    this.score += points;
    this.updateScoreDisplay();
    if (animationConfig) {
      this.createScoreAnimation(animationConfig);
    }
    this.events.emit("scoreChanged", this.score, points);
  }
  /**
   * Set the score to a specific value
   */
  setScore(newScore) {
    const oldScore = this.score;
    this.score = newScore;
    this.updateScoreDisplay();
    this.events.emit("scoreChanged", this.score, this.score - oldScore);
  }
  /**
   * Get the current score
   */
  getScore() {
    return this.score;
  }
  /**
   * Reset score to initial value
   */
  reset() {
    this.score = this.config.initialScore;
    this.updateScoreDisplay();
    this.events.emit("scoreReset", this.score);
  }
  /**
   * Update the score display text
   */
  updateScoreDisplay() {
    if (this.scoreText) {
      this.scoreText.setText(this.score.toString());
    }
  }
  /**
   * Create animated flying score text
   */
  createScoreAnimation(config) {
    const animationText = this.scene.add.text(
      config.startX,
      config.startY,
      `+${config.points}`,
      {
        fontFamily: this.config.fontFamily,
        fontSize: "24px",
        color: config.color ?? this.config.animationColor,
        stroke: "#000000",
        strokeThickness: 3
      }
    );
    animationText.setOrigin(0.5);
    this.scene.tweens.add({
      targets: animationText,
      y: config.startY - 50,
      alpha: 0,
      scale: 1.2,
      duration: config.duration ?? this.config.animationDuration,
      ease: "Power2",
      onComplete: () => {
        animationText.destroy();
      }
    });
  }
  /**
   * Subscribe to score events
   */
  on(event, callback) {
    this.events.on(event, callback);
  }
  /**
   * Unsubscribe from score events
   */
  off(event, callback) {
    this.events.off(event, callback);
  }
  /**
   * Clean up resources
   */
  destroy() {
    this.events.removeAllListeners();
    if (this.container) {
      this.container.destroy();
    }
    this.scoreText = void 0;
    this.scoreLabel = void 0;
    this.container = void 0;
  }
}
class TimerManager3 {
  scene;
  config;
  // Timer state
  startTime = 0;
  isRunning = false;
  isFinished = false;
  // UI Elements
  timeText;
  container;
  // Timer event
  timerEvent;
  // Events
  events;
  constructor(scene, config) {
    this.scene = scene;
    this.events = new Phaser$1.Events.EventEmitter();
    this.config = {
      duration: config.duration,
      updateInterval: config.updateInterval ?? 100,
      fontFamily: config.fontFamily ?? "Arial",
      fontSize: config.fontSize ?? "24px",
      normalColor: config.normalColor ?? "#FFFFFF",
      warningColor: config.warningColor ?? "#ff0000",
      warningThreshold: config.warningThreshold ?? 5
    };
  }
  /**
   * Create the timer UI at the specified position
   */
  createUI(x, y, parentContainer) {
    this.container = this.scene.add.container(0, 0);
    this.timeText = this.scene.add.text(x, y, this.formatTime(this.config.duration), {
      fontFamily: this.config.fontFamily,
      fontSize: this.config.fontSize,
      fontStyle: "bold",
      color: this.config.normalColor
    }).setOrigin(0.5);
    this.container.add(this.timeText);
    if (parentContainer) {
      parentContainer.add(this.container);
    }
  }
  /**
   * Start the countdown timer
   */
  start() {
    if (this.isRunning) return;
    this.startTime = this.scene.time.now;
    this.isFinished = false;
    this.isRunning = true;
    if (this.timerEvent) {
      this.timerEvent.destroy();
    }
    this.timerEvent = this.scene.time.addEvent({
      delay: this.config.updateInterval,
      callback: this.updateTimer.bind(this),
      loop: true
    });
    this.events.emit("timerStarted");
  }
  /**
   * Stop and reset the timer
   */
  stop() {
    if (this.timerEvent) {
      this.timerEvent.destroy();
      this.timerEvent = void 0;
    }
    this.events.emit("timerStopped");
  }
  /**
   * Get current timer state
   */
  getState() {
    const timeRemaining = this.getTimeRemaining();
    const progress = timeRemaining / this.config.duration;
    return {
      timeRemaining,
      totalDuration: this.config.duration,
      isRunning: this.isRunning,
      isFinished: this.isFinished,
      progress: Math.max(0, Math.min(1, progress))
    };
  }
  /**
   * Get time remaining in seconds
   */
  getTimeRemaining() {
    if (!this.isRunning) return this.config.duration;
    const elapsed = this.scene.time.now - this.startTime;
    return Math.max(0, this.config.duration - elapsed / 1e3);
  }
  /**
   * Update timer display and check for completion
   */
  updateTimer() {
    const timeRemaining = this.getTimeRemaining();
    const seconds = Math.ceil(timeRemaining);
    if (this.timeText) {
      this.timeText.setText(this.formatTime(seconds));
      const color = seconds <= this.config.warningThreshold ? this.config.warningColor : this.config.normalColor;
      this.timeText.setColor(color);
    }
    const state = this.getState();
    this.events.emit("timerUpdate", state);
    if (timeRemaining <= 0 && !this.isFinished) {
      this.isFinished = true;
      this.isRunning = false;
      if (this.timerEvent) {
        this.timerEvent.destroy();
        this.timerEvent = void 0;
      }
      this.events.emit("timeUp");
    }
  }
  /**
   * Format time in seconds to display string
   */
  formatTime(seconds) {
    return `${Math.max(0, Math.floor(seconds))}s`;
  }
  /**
   * Subscribe to timer events
   */
  on(event, callback) {
    this.events.on(event, callback);
  }
  /**
   * Unsubscribe from timer events
   */
  off(event, callback) {
    this.events.off(event, callback);
  }
  /**
   * Clean up resources
   */
  destroy() {
    this.stop();
    this.events.removeAllListeners();
    if (this.container) {
      this.container.destroy();
    }
    this.timeText = void 0;
    this.container = void 0;
  }
}
class LivesManager3 {
  scene;
  config;
  lives;
  // UI Elements
  hearts = [];
  container;
  // Events
  events;
  constructor(scene, config = {}) {
    this.scene = scene;
    this.events = new Phaser$1.Events.EventEmitter();
    this.config = {
      initialLives: config.initialLives ?? 3
    };
    this.lives = this.config.initialLives;
  }
  /**
   * Create the lives UI elements at the specified position
   */
  createUI(x, y, parentContainer) {
    this.container = this.scene.add.container(x, y);
    const totalWidth = (this.lives - 1) * 40;
    const startX = -totalWidth / 2;
    for (let i = 0; i < this.lives; i++) {
      let heart = this.scene.add.image(
        startX + i * 40,
        0,
        "heart"
      ).setOrigin(0.5).setScale(1.5);
      this.hearts.push(heart);
    }
    this.container.add(this.hearts);
    if (parentContainer) {
      parentContainer.add(this.container);
    }
  }
  deductHeart(fromX, fromY) {
    this.lives--;
    if (this.hearts[this.lives]) {
      this.hearts[this.lives].setTexture("heart_outline");
    }
    if (fromX !== void 0 && fromY !== void 0 && this.container) {
      this.createFlyingHeartAnimation(fromX, fromY);
    }
    this.events.emit("heartDeducted", this.lives);
  }
  /**
   * Create animated flying heart_broken image
   */
  createFlyingHeartAnimation(startX, startY) {
    if (!this.container) return;
    const flyingHeart = this.scene.add.image(startX, startY, "heart_broken").setOrigin(0.5).setScale(1.5).setAlpha(0.4);
    this.scene.tweens.add({
      targets: flyingHeart,
      y: startY - 200,
      scale: 3,
      alpha: 0.8,
      duration: 600,
      ease: "Power2",
      onComplete: () => {
        flyingHeart.destroy();
      }
    });
  }
  /**
   * Subscribe to score events
   */
  on(event, callback) {
    this.events.on(event, callback);
  }
  /**
   * Unsubscribe from score events
   */
  off(event, callback) {
    this.events.off(event, callback);
  }
  /**
   * Clean up resources
   */
  destroy() {
    this.events.removeAllListeners();
    if (this.container) {
      this.container.destroy();
    }
    this.container = void 0;
  }
}
class TimerBarUI3 {
  scene;
  config;
  // UI Elements
  container;
  timerBar;
  timerMask;
  timerIcon;
  leftCircle;
  rightCircle;
  background;
  // Gradient texture name
  gradientTextureName;
  constructor(scene, config) {
    this.scene = scene;
    this.gradientTextureName = `timerBarGradient_${Date.now()}_${Math.random()}`;
    this.config = {
      width: config.width,
      height: config.height,
      x: config.x,
      y: config.y,
      backgroundColor: config.backgroundColor ?? 1118481,
      gradientStartColor: config.gradientStartColor ?? "#33DDFF",
      gradientEndColor: config.gradientEndColor ?? "#664DFF",
      cornerRadius: config.cornerRadius ?? 10,
      showTimerIcon: config.showTimerIcon ?? true,
      showCircularBg: config.showCircularBg ?? true,
      circleRadius: config.circleRadius ?? 30,
      circleBorderColor: config.circleBorderColor ?? 3399167,
      circleBorderWidth: config.circleBorderWidth ?? 2
    };
  }
  /**
   * Create the timer bar UI elements
   */
  create(parentContainer) {
    this.container = this.scene.add.container(0, 0);
    this.background = this.scene.add.rectangle(
      this.config.x,
      this.config.y,
      this.config.width,
      this.config.height,
      this.config.backgroundColor
    ).setOrigin(0.5);
    this.container.add(this.background);
    this.createGradientTexture();
    this.timerBar = this.scene.add.image(this.config.x, this.config.y, this.gradientTextureName).setOrigin(0.5);
    this.container.add(this.timerBar);
    this.createTimerMask();
    if (this.config.showCircularBg) {
      this.createCircularBackgrounds();
    }
    if (this.config.showTimerIcon) {
      this.createTimerIcon();
    }
    if (parentContainer) {
      parentContainer.add(this.container);
    }
  }
  /**
   * Update the progress of the timer bar (0-1)
   */
  updateProgress(progress) {
    if (!this.timerMask || !this.timerBar) return;
    progress = Math.max(0, Math.min(1, progress));
    const barWidth = this.config.width - 4;
    const barHeight = this.config.height - 4;
    const visibleWidth = barWidth * progress;
    this.timerMask.clear();
    if (visibleWidth > 0) {
      this.timerMask.fillStyle(16777215, 1);
      this.timerMask.fillRoundedRect(
        this.config.x - barWidth / 2,
        this.config.y - barHeight / 2,
        visibleWidth,
        barHeight,
        this.config.cornerRadius
      );
    }
  }
  /**
   * Create the gradient texture for the timer bar
   */
  createGradientTexture() {
    const barWidth = this.config.width - 4;
    const barHeight = this.config.height - 4;
    const barTexture = this.scene.textures.createCanvas(this.gradientTextureName, barWidth, barHeight);
    const barContext = barTexture?.getContext();
    if (barContext && barTexture) {
      const gradient = barContext.createLinearGradient(0, 0, barWidth, 0);
      gradient.addColorStop(0, this.config.gradientStartColor);
      gradient.addColorStop(1, this.config.gradientEndColor);
      barContext.fillStyle = gradient;
      barContext.fillRect(0, 0, barWidth, barHeight);
      barTexture.refresh();
    }
  }
  /**
   * Create the mask for the timer bar progress
   */
  createTimerMask() {
    if (!this.timerBar) return;
    const barWidth = this.config.width - 4;
    const barHeight = this.config.height - 4;
    this.timerMask = this.scene.make.graphics({});
    this.timerMask.fillRect(
      this.config.x - barWidth / 2,
      this.config.y - barHeight / 2,
      barWidth,
      barHeight
    );
    const mask = new Phaser$1.Display.Masks.GeometryMask(this.scene, this.timerMask);
    this.timerBar.setMask(mask);
  }
  /**
   * Create circular backgrounds on left and right
   */
  createCircularBackgrounds() {
    if (!this.container) return;
    const leftX = this.config.x - this.config.width / 2;
    const rightX = this.config.x + this.config.width / 2;
    this.leftCircle = this.scene.add.circle(
      leftX,
      this.config.y,
      this.config.circleRadius,
      2236962
    );
    this.leftCircle.setStrokeStyle(this.config.circleBorderWidth, this.config.circleBorderColor, 1);
    this.container.add(this.leftCircle);
    this.rightCircle = this.scene.add.circle(
      rightX,
      this.config.y,
      this.config.circleRadius,
      2236962
    );
    this.rightCircle.setStrokeStyle(this.config.circleBorderWidth, 6704639, 1);
    this.container.add(this.rightCircle);
  }
  /**
   * Create timer icon
   */
  createTimerIcon() {
    if (!this.container) return;
    const leftX = this.config.x - this.config.width / 2;
    if (this.scene.textures.exists("timer_icon")) {
      this.timerIcon = this.scene.add.image(leftX, this.config.y, "timer_icon").setOrigin(0.5).setScale(0.5);
    } else {
      const clockGraphics = this.scene.add.graphics();
      clockGraphics.lineStyle(2, 3399167);
      clockGraphics.strokeCircle(0, 0, 12);
      clockGraphics.moveTo(0, 0);
      clockGraphics.lineTo(0, -8);
      clockGraphics.moveTo(0, 0);
      clockGraphics.lineTo(6, 0);
      clockGraphics.setPosition(leftX, this.config.y);
      this.container.add(clockGraphics);
    }
    if (this.timerIcon) {
      this.container.add(this.timerIcon);
    }
  }
  /**
   * Set the gradient colors
   */
  setGradientColors(startColor, endColor) {
    this.config.gradientStartColor = startColor;
    this.config.gradientEndColor = endColor;
    if (this.scene.textures.exists(this.gradientTextureName)) {
      this.scene.textures.remove(this.gradientTextureName);
    }
    this.createGradientTexture();
    if (this.timerBar) {
      this.timerBar.setTexture(this.gradientTextureName);
    }
  }
  /**
   * Get the container for additional positioning/effects
   */
  getContainer() {
    return this.container;
  }
  /**
   * Get the right circle position for timer text placement
   */
  getRightCirclePosition() {
    return {
      x: this.config.x + this.config.width / 2,
      y: this.config.y
    };
  }
  /**
   * Clean up resources
   */
  destroy() {
    if (this.scene.textures.exists(this.gradientTextureName)) {
      this.scene.textures.remove(this.gradientTextureName);
    }
    if (this.container) {
      this.container.destroy();
    }
    this.timerBar = void 0;
    this.timerMask = void 0;
    this.timerIcon = void 0;
    this.leftCircle = void 0;
    this.rightCircle = void 0;
    this.background = void 0;
    this.container = void 0;
  }
}
class GameScene4 extends Phaser$1.Scene {
  ticTaps;
  gamePanel;
  countdownPanel;
  numberObjects = [];
  currentIndex = 0;
  isLocked = false;
  roundText;
  scoreManager;
  timerManager;
  livesManager;
  timerBarUI;
  line;
  round = 1;
  countPerRound = {
    1: 5,
    2: 7,
    3: 9,
    4: 12
  };
  confettiEmitter;
  constructor() {
    super("GameScene");
  }
  /**
   * Helper method to play sound effects with proper error handling
   */
  playSound(soundEffect) {
    try {
      this.sound.play(soundEffect);
    } catch (error) {
      console.warn("Sound playback failed:", error);
    }
  }
  init() {
    this.currentIndex = 0;
    this.isLocked = false;
    this.round = 1;
    this.scoreManager = new ScoreManager4(this, {
      initialScore: 0,
      fontSize: "80px",
      scoreColor: "#33DDFF"
    });
    this.timerManager = new TimerManager3(this, {
      duration: GameConfig2.GAME_DURATION,
      warningThreshold: 5
    });
    this.livesManager = new LivesManager3(this, {
      initialLives: 3
    });
    this.timerBarUI = new TimerBarUI3(this, {
      width: this.cameras.main.width * 0.8,
      height: 35,
      x: this.cameras.main.width / 2,
      y: this.cameras.main.height * 0.07
    });
    this.timerManager.on("timeUp", () => this.timeOut());
    this.timerManager.on("timerUpdate", (state) => {
      this.timerBarUI.updateProgress(state.progress);
    });
    this.livesManager.on("heartDeducted", (lives) => {
      if (lives === 0) {
        this.timeOut();
      }
    });
  }
  create() {
    this.ticTaps = new TicTapsConnector4();
    this.add.image(this.cameras.main.width / 2, this.cameras.main.height / 2, "game_background").setOrigin(0.5).setDisplaySize(this.cameras.main.width, this.cameras.main.height);
    this.createCountdownPanel();
    this.createGamePanel();
    this.line = this.add.graphics();
    this.createConfettiSystem();
    this.time.delayedCall(100, () => {
      this.startCountdown();
    });
  }
  shutdown() {
    if (this.scoreManager) {
      this.scoreManager.destroy();
    }
    if (this.timerManager) {
      this.timerManager.destroy();
    }
    if (this.timerBarUI) {
      this.timerBarUI.destroy();
    }
    if (this.livesManager) {
      this.livesManager.destroy();
    }
    if (this.confettiEmitter) {
      this.confettiEmitter.stop();
    }
    this.numberObjects.forEach((obj) => obj.destroy());
    this.numberObjects = [];
  }
  createUI() {
    const { width, height } = this.cameras.main;
    const timerContainer = this.add.container(0, 0);
    this.gamePanel.add(timerContainer);
    this.timerBarUI.create(timerContainer);
    const rightCirclePos = this.timerBarUI.getRightCirclePosition();
    this.timerManager.createUI(rightCirclePos.x, rightCirclePos.y, timerContainer);
    const timerBarY = height * 0.07;
    this.scoreManager.createUI(width / 2, timerBarY + 120, timerContainer);
    this.livesManager.createUI(width / 2, timerBarY + 50, timerContainer);
  }
  createCountdownPanel() {
    const { width, height } = this.cameras.main;
    this.countdownPanel = this.add.container(width / 2, height / 2);
    const countdownBg = this.add.graphics();
    countdownBg.fillStyle(0, 0.8);
    countdownBg.fillRect(-width / 2, -height / 2, width, height);
    this.countdownPanel.add(countdownBg);
    const countdownImage = this.add.image(0, 0, "countdown-3").setScale(0.27);
    this.countdownPanel.add(countdownImage);
    this.countdownPanel.setData("image", countdownImage);
  }
  createGamePanel() {
    this.gamePanel = this.add.container(0, 0);
    this.gamePanel.visible = false;
    this.createUI();
    this.createGradientRoundText();
    this.gamePanel.add([]);
  }
  /**
   * Create gradient round text similar to EndGameScene
   */
  createGradientRoundText() {
    this.updateGradientRoundText("0 to 6");
  }
  /**
   * Update the gradient round text with new content
   */
  updateGradientRoundText(text) {
    const { height } = this.cameras.main;
    const textureName = "roundText-" + text.replace(/\s+/g, "-");
    if (this.textures.exists(textureName)) {
      this.textures.remove(textureName);
    }
    const fontSize = 24;
    const width = Math.max(120, text.length * fontSize * 0.6);
    const textHeight = fontSize * 1.5;
    const textCanvas = this.textures.createCanvas(textureName, width, textHeight);
    if (!textCanvas) {
      console.error("Failed to create gradient round text canvas");
      if (this.roundText) {
        this.roundText.destroy();
      }
      this.roundText = this.add.text(50, height - 20, text, {
        fontSize: "24px",
        fontFamily: "Arial",
        color: "#999999"
      }).setOrigin(0, 1);
      this.gamePanel.add(this.roundText);
      return;
    }
    const context = textCanvas.getContext();
    const gradient = context.createLinearGradient(0, 0, width, textHeight * 0.5);
    gradient.addColorStop(0, "#32c4ff");
    gradient.addColorStop(0.5, "#7f54ff");
    gradient.addColorStop(1, "#b63efc");
    context.font = `bold ${fontSize}px Arial`;
    context.textAlign = "left";
    context.textBaseline = "middle";
    context.fillStyle = gradient;
    context.fillText(text, 0, textHeight / 2);
    textCanvas.refresh();
    if (this.roundText) {
      this.roundText.destroy();
    }
    this.roundText = this.add.image(50, height - 20, textureName).setOrigin(0, 1);
    this.gamePanel.add(this.roundText);
  }
  async startCountdown() {
    const countdownImages = ["countdown-3", "countdown-2", "countdown-1", "countdown-go"];
    const countdownImage = this.countdownPanel.getData("image");
    for (let i = 0; i < countdownImages.length; i++) {
      await this.playCountdownStep(countdownImage, countdownImages[i], i === countdownImages.length - 1);
    }
    this.countdownPanel.visible = false;
    this.gamePanel.visible = true;
    this.generateNumbers(this.countPerRound[this.round]);
    this.startTimer();
  }
  playCountdownStep(image, texture, isGo) {
    return new Promise((resolve) => {
      image.setTexture(texture);
      image.setScale(0);
      this.playSound(isGo ? "go" : "countdown");
      this.tweens.add({
        targets: image,
        scale: 0.27,
        duration: 300,
        ease: "Back.easeOut",
        onComplete: () => {
          this.time.delayedCall(700, () => {
            this.tweens.add({
              targets: image,
              scale: 0,
              duration: 300,
              ease: "Back.easeIn",
              onComplete: () => resolve()
            });
          });
        }
      });
    });
  }
  generateNumbers(count) {
    this.numberObjects.forEach((obj) => obj.destroy());
    this.numberObjects = [];
    this.currentIndex = 0;
    this.line.clear();
    this.updateGradientRoundText(`0 to ${count}`);
    const positions = this.generatePositions(count);
    for (let i = 0; i < count; i++) {
      this.time.delayedCall(i * 100, () => {
        const numberObj = new NumberObject(this, positions[i].x, positions[i].y, i);
        this.numberObjects.push(numberObj);
      });
    }
  }
  generatePositions(count) {
    const { width, height } = this.cameras.main;
    const circleRadius = GameConfig2.CIRCLE_RADIUS;
    const minDistance = circleRadius * 2 + 20;
    const gameAreaStart = height * 0.3;
    const gameAreaEnd = height * 0.95;
    const leftBoundary = width * 0.06;
    const rightBoundary = width * 0.94;
    const positions = [];
    const isPositionValid = (newX, newY) => {
      if (newX - circleRadius < leftBoundary || newX + circleRadius > rightBoundary || newY - circleRadius < gameAreaStart || newY + circleRadius > gameAreaEnd) {
        return false;
      }
      for (const pos of positions) {
        const distance = Math.sqrt(
          Math.pow(newX - pos.x, 2) + Math.pow(newY - pos.y, 2)
        );
        if (distance < minDistance) {
          return false;
        }
      }
      return true;
    };
    let attempts = 0;
    const maxAttempts = 1e3;
    while (positions.length < count && attempts < maxAttempts) {
      const randomX = leftBoundary + circleRadius + Math.random() * (rightBoundary - leftBoundary - circleRadius * 2);
      const randomY = gameAreaStart + circleRadius + Math.random() * (gameAreaEnd - gameAreaStart - circleRadius * 2);
      if (isPositionValid(randomX, randomY)) {
        positions.push({ x: randomX, y: randomY });
      }
      attempts++;
    }
    if (positions.length < count) {
      console.warn("Using grid fallback for remaining positions");
      return this.generateGridBasedPositions(count);
    }
    return positions;
  }
  generateGridBasedPositions(count) {
    const { width, height } = this.cameras.main;
    const circleDiameter = GameConfig2.CIRCLE_RADIUS * 2;
    const gridWidth = Math.min(5, Math.floor(width / circleDiameter));
    const gridHeight = Math.ceil(count / gridWidth);
    const totalWidth = gridWidth * circleDiameter;
    const totalHeight = gridHeight * circleDiameter;
    const startX = (width - totalWidth) / 2 + circleDiameter / 2;
    const gameAreaHeight = height * 0.75;
    const gameAreaStart = height * 0.2;
    const availableHeight = gameAreaHeight;
    const startY = gameAreaStart + (availableHeight - totalHeight) / 2;
    const gridPositions = [];
    for (let i = 0; i < count; i++) {
      const row = Math.floor(i / gridWidth);
      const col = i % gridWidth;
      gridPositions.push({
        x: startX + col * circleDiameter,
        y: startY + row * circleDiameter
      });
    }
    const shuffled = [...gridPositions].sort(() => Math.random() - 0.5);
    return shuffled;
  }
  startTimer() {
    this.timerManager.start();
  }
  timeOut() {
    this.isLocked = true;
    this.timerManager.stop();
    this.playSound("timeout");
    this.confettiEmitter.start();
    this.time.delayedCall(1500, () => {
      this.scene.start("GameEndScene", { score: this.scoreManager.getScore() });
    });
  }
  handleNumberClick(numberObject) {
    if (this.isLocked) return;
    if (numberObject.numberValue === this.currentIndex) {
      this.handleCorrectNumber(numberObject);
    } else {
      this.handleWrongNumber(numberObject);
    }
  }
  handleCorrectNumber(numberObject) {
    numberObject.setActive(true);
    this.updateLine(numberObject.x, numberObject.y);
    this.playSound("collect");
    this.scoreManager.addPoints(this.round, {
      startX: numberObject.x,
      startY: numberObject.y,
      points: this.round
    });
    this.currentIndex++;
    this.updateGradientRoundText(`${this.currentIndex} to ${this.countPerRound[this.round]}`);
    const roundCompleted = this.currentIndex === this.countPerRound[this.round];
    numberObject.playDisappearAnimation(() => {
      if (roundCompleted) {
        if (this.round > 3) {
          this.gameComplete();
        } else {
          this.roundComplete();
        }
      }
    });
  }
  handleWrongNumber(numberObject) {
    numberObject.setError();
    this.playSound("error");
    this.livesManager.deductHeart(
      numberObject.x,
      numberObject.y
    );
  }
  updateLine(x, y) {
    if (this.currentIndex === 1) {
      this.line.lineStyle(4, 52479);
      this.line.moveTo(this.numberObjects[0].x, this.numberObjects[0].y);
    }
    this.line.lineTo(x, y);
  }
  roundComplete() {
    this.round++;
    if (this.round > Object.keys(this.countPerRound).length) {
      this.gameComplete();
    } else {
      const lastNumberIndex = this.currentIndex - 1;
      const lastNumberObject = this.numberObjects[lastNumberIndex];
      if (lastNumberObject) {
        this.confettiEmitter.setPosition(lastNumberObject.x, lastNumberObject.y);
        this.confettiEmitter.explode(25);
      }
      this.playSound("complete");
      this.time.delayedCall(500, () => {
        const count = this.countPerRound[this.round];
        this.generateNumbers(count);
        this.updateGradientRoundText(`0 to ${count}`);
      });
    }
  }
  gameComplete() {
    this.isLocked = true;
    this.timerManager.stop();
    const lastNumberIndex = this.currentIndex - 1;
    const lastNumberObject = this.numberObjects[lastNumberIndex];
    if (lastNumberObject) {
      this.confettiEmitter.setPosition(lastNumberObject.x, lastNumberObject.y);
      this.confettiEmitter.explode(25);
    }
    const timerState = this.timerManager.getState();
    const timeRemainingInSeconds = Math.floor(timerState.timeRemaining);
    console.log("Time remaining:", timeRemainingInSeconds);
    const timeMultiplier = Math.max(1, timeRemainingInSeconds);
    const currentScore = this.scoreManager.getScore();
    const finalScore = timerState.timeRemaining > 0 ? currentScore * timeMultiplier : currentScore;
    this.playSound("complete");
    this.time.delayedCall(3e3, () => {
      this.scene.start("GameEndScene", { score: finalScore });
    });
  }
  createConfettiSystem() {
    const { width } = this.cameras.main;
    const texture = this.textures.createCanvas("particleTexture", 10, 10);
    if (!texture) {
      console.warn("Failed to create particle texture, using fallback");
      this.confettiEmitter = this.add.particles(width / 2, -50, "", {
        speed: { min: 150, max: 350 },
        scale: { start: 0.8, end: 0 },
        lifespan: { min: 1500, max: 2500 },
        gravityY: 400,
        rotate: { min: -180, max: 180 },
        frequency: 50,
        quantity: 3,
        tint: [16711680, 65280, 255, 16776960, 16711935, 65535],
        blendMode: "ADD",
        emitting: false
      });
      return;
    }
    const context = texture.getContext();
    context.fillStyle = "#ffffff";
    context.fillRect(0, 0, 10, 10);
    texture.refresh();
    this.confettiEmitter = this.add.particles(width / 2, -50, "particleTexture", {
      speed: { min: 150, max: 350 },
      // Outward explosion speed
      scale: { start: 0.8, end: 0 },
      // Start bigger, shrink to nothing
      lifespan: { min: 1500, max: 2500 },
      // How long particles live
      gravityY: 400,
      // Gravity pulls them down after explosion
      scaleX: {
        onUpdate: (_particle, _key, t) => {
          return Math.sin(t * Math.PI * 8);
        }
      },
      rotate: { min: -180, max: 180 },
      // Random rotation
      frequency: 50,
      // For continuous emission (not used for explode)
      quantity: 3,
      // For continuous emission (not used for explode)
      tint: [16711680, 65280, 255, 16776960, 16711935, 65535],
      blendMode: "ADD",
      emitting: false
      // We'll use explode() method instead
    });
  }
}
class GameEndScene4 extends Phaser.Scene {
  ticTaps;
  score = 0;
  backToLobbyButton;
  constructor() {
    super("GameEndScene");
  }
  init(data) {
    this.score = data.score || 0;
  }
  create() {
    this.ticTaps = new TicTapsConnector4();
    this.add.image(0, 0, "game_background").setOrigin(0, 0).setDisplaySize(this.cameras.main.width, this.cameras.main.height);
    const panelWidth = this.cameras.main.width * 0.8;
    const panelHeight = this.cameras.main.height * 0.6;
    if (this.sys.game.renderer.type === Phaser.WEBGL) {
      const blurBg = this.add.graphics();
      blurBg.fillStyle(0, 0.3);
      blurBg.fillRoundedRect(
        this.cameras.main.width / 2 - panelWidth / 2 - 2,
        this.cameras.main.height / 2 - panelHeight / 2 - 2,
        panelWidth + 4,
        panelHeight + 4,
        20
      );
      blurBg.postFX.addBlur(0, 0, 1, 2, 1, 1);
    }
    const panel = this.add.graphics();
    panel.fillStyle(1712945, 0.4);
    panel.fillRoundedRect(
      this.cameras.main.width / 2 - panelWidth / 2,
      this.cameras.main.height / 2 - panelHeight / 2,
      panelWidth,
      panelHeight,
      20
    );
    const gameOverImage = this.add.image(
      this.cameras.main.width / 2,
      this.cameras.main.height / 2 - panelHeight * 0.5,
      // Positioned at top of panel
      "game_over"
    ).setOrigin(0.5);
    const gameOverScale = panelWidth * 0.8 / gameOverImage.width;
    gameOverImage.setScale(gameOverScale);
    if (this.sys.game.renderer.type === Phaser.WEBGL) {
      gameOverImage.postFX.addGlow(4980654, 1, 0, false, 0.1, 15);
    }
    this.add.text(
      this.cameras.main.width / 2,
      this.cameras.main.height / 2 - 100,
      // Slight offset above center
      "SCORE",
      {
        fontFamily: "Arial",
        fontSize: "30px",
        fontStyle: "bold",
        color: "#FFFFFF"
      }
    ).setOrigin(0.5);
    this.createGradientText(
      this.cameras.main.width / 2,
      this.cameras.main.height / 2,
      // Below "SCORE" text
      this.score.toString(),
      90,
      true
      // Make it with gradient and white outline
    );
    this.createBackToLobbyButton();
  }
  /**
   * Create the Back to Lobby button with gradient border as in Image 2
   */
  createBackToLobbyButton() {
    const buttonWidth = this.cameras.main.width * 0.7;
    const buttonHeight = 80;
    const buttonX = this.cameras.main.width / 2;
    const buttonY = this.cameras.main.height / 2 + this.cameras.main.height * 0.2;
    this.backToLobbyButton = this.add.container(buttonX, buttonY);
    const borderCanvas = this.textures.createCanvas("buttonBorder", buttonWidth + 4, buttonHeight + 4);
    if (borderCanvas) {
      const borderContext = borderCanvas.getContext();
      const gradient = borderContext.createLinearGradient(0, 0, buttonWidth + 4, 0);
      gradient.addColorStop(0, "#32c4ff");
      gradient.addColorStop(0.5, "#7f54ff");
      gradient.addColorStop(1, "#b63efc");
      borderContext.strokeStyle = gradient;
      borderContext.lineWidth = 2.5;
      roundRect(borderContext, 2, 2, buttonWidth, buttonHeight, 18);
      borderCanvas.refresh();
      const border = this.add.image(0, 0, "buttonBorder").setOrigin(0.5);
      this.backToLobbyButton.add(border);
    }
    const buttonBg = this.add.graphics();
    buttonBg.fillStyle(1185311, 1);
    buttonBg.fillRoundedRect(-buttonWidth / 2 + 2, -buttonHeight / 2 + 2, buttonWidth - 4, buttonHeight - 4, 16);
    this.backToLobbyButton.add(buttonBg);
    if (this.textures.exists("back_to_lobby")) {
      const buttonText = this.add.image(0, 0, "back_to_lobby").setOrigin(0.5);
      const textScale = Math.min(buttonWidth * 0.7 / buttonText.width, buttonHeight * 0.6 / buttonText.height);
      buttonText.setScale(textScale);
      this.backToLobbyButton.add(buttonText);
    } else {
      const buttonText = this.createGradientText(0, 0, "BACK TO LOBBY", 28, false, true);
      this.backToLobbyButton.add(buttonText);
    }
    const hitArea = new Phaser.Geom.Rectangle(-buttonWidth / 2, -buttonHeight / 2, buttonWidth, buttonHeight);
    this.backToLobbyButton.setInteractive(hitArea, Phaser.Geom.Rectangle.Contains);
    this.backToLobbyButton.on("pointerover", () => {
      this.backToLobbyButton.setScale(1.05);
    });
    this.backToLobbyButton.on("pointerout", () => {
      this.backToLobbyButton.setScale(1);
    });
    this.backToLobbyButton.on("pointerdown", () => {
      if (this.sound.get("laser")) {
        this.sound.play("laser", { volume: 0.7 });
      } else if (this.sound.get("countdown")) {
        this.sound.play("countdown", { volume: 0.7 });
      }
      this.backToLobbyButton.setScale(0.95);
      this.time.delayedCall(100, () => {
        this.backToLobbyButton.setScale(1);
        this.endGame();
      });
    });
  }
  /**
   * Helper function to create gradient text
   */
  createGradientText(x, y, text, fontSize = 32, isScoreText = false, isButtonText = false) {
    const textureName = "gradientText-" + text.replace(/\s+/g, "-") + "-" + fontSize + (isScoreText ? "-score" : "") + (isButtonText ? "-button" : "");
    if (this.textures.exists(textureName)) {
      this.textures.remove(textureName);
    }
    const width = Math.max(400, text.length * fontSize * 0.7);
    const height = fontSize * 1.5;
    const textCanvas = this.textures.createCanvas(textureName, width, height);
    if (!textCanvas) {
      console.error("Failed to create gradient text canvas");
      return this.add.image(x, y, "").setOrigin(0.5);
    }
    const context = textCanvas.getContext();
    const gradient = context.createLinearGradient(0, 0, width, height * 0.5);
    if (isScoreText) {
      gradient.addColorStop(0, "#4cffae");
      gradient.addColorStop(0.4, "#32c4ff");
      gradient.addColorStop(1, "#5c67ff");
    } else if (isButtonText) {
      gradient.addColorStop(0, "#32c4ff");
      gradient.addColorStop(0.5, "#7f54ff");
      gradient.addColorStop(1, "#b63efc");
    } else {
      gradient.addColorStop(0, "#33DDFF");
      gradient.addColorStop(1, "#664DFF");
    }
    context.font = `bold ${fontSize}px Arial`;
    context.textAlign = "center";
    context.textBaseline = "middle";
    if (isScoreText) {
      context.strokeStyle = "rgba(255, 255, 255, 0.9)";
      context.lineWidth = 5;
      context.strokeText(text, width / 2, height / 2);
    }
    context.fillStyle = gradient;
    context.fillText(text, width / 2, height / 2);
    textCanvas.refresh();
    return this.add.image(x, y, textureName).setOrigin(0.5);
  }
  endGame() {
    const flash = this.add.rectangle(
      this.cameras.main.width / 2,
      this.cameras.main.height / 2,
      this.cameras.main.width,
      this.cameras.main.height,
      16777215
    ).setAlpha(0).setOrigin(0.5);
    flash.setDepth(1e3);
    this.ticTaps.sendScore(this.score);
    this.ticTaps.notifyGameQuit();
    this.tweens.add({
      targets: flash,
      alpha: 0.8,
      duration: 100,
      ease: "Sine.easeOut",
      onComplete: () => {
        this.tweens.add({
          targets: flash,
          alpha: 0,
          delay: 50,
          duration: 250,
          ease: "Sine.easeIn",
          onComplete: () => {
            this.scene.start("GameStartScene");
          }
        });
      }
    });
  }
}
function roundRect(ctx, x, y, width, height, radius, fill, stroke) {
  ctx.beginPath();
  ctx.moveTo(x + radius, y);
  ctx.lineTo(x + width - radius, y);
  ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
  ctx.lineTo(x + width, y + height - radius);
  ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
  ctx.lineTo(x + radius, y + height);
  ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
  ctx.lineTo(x, y + radius);
  ctx.quadraticCurveTo(x, y, x + radius, y);
  ctx.closePath();
  {
    ctx.stroke();
  }
}
function Preloading($$payload, $$props) {
  let { progress } = $$props;
  let isComplete = progress >= 1;
  if (
    // $: isComplete = progress >= 1;
    !isComplete
  ) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="absolute w-screen h-screen z-1000 flex flex-col justify-center items-center"><div class="background svelte-2ea9pu"></div>  <div class="w-80 h-4 bg-gray-700 rounded-full overflow-hidden"><div class="h-full bg-gradient-to-r from-blue-500 to-green-500 rounded-full transition-all duration-300 ease-out"${attr_style(`width: ${stringify(Math.max(0, Math.min(100, progress * 100)))}%`)}></div></div> <p class="text-sm text-gray-300 mt-4">${escape_html(Math.round(Math.max(0, Math.min(100, progress * 100))))}%</p></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]-->`);
}
function StartScreen($$payload, $$props) {
  let { gameId } = $$props;
  let isStarting = false;
  $$payload.out.push(`<div class="game-start-container svelte-2lsvf3"><div class="background svelte-2lsvf3"></div> <img class="game-title pulse svelte-2lsvf3"${attr("src", `/assets-${stringify(gameId)}/images/game_name.png`)} alt="Game Title"/> <button${attr_class("start-button svelte-2lsvf3", void 0, { "clicked": isStarting })}${attr("disabled", isStarting, true)}><div class="btn-border svelte-2lsvf3"></div> <div class="btn-background svelte-2lsvf3"></div> <span class="btn-text svelte-2lsvf3">START GAME</span></button></div>`);
}
const matchIconName = /^[a-z0-9]+(-[a-z0-9]+)*$/;
const stringToIcon = (value, validate, allowSimpleName, provider = "") => {
  const colonSeparated = value.split(":");
  if (value.slice(0, 1) === "@") {
    if (colonSeparated.length < 2 || colonSeparated.length > 3) {
      return null;
    }
    provider = colonSeparated.shift().slice(1);
  }
  if (colonSeparated.length > 3 || !colonSeparated.length) {
    return null;
  }
  if (colonSeparated.length > 1) {
    const name2 = colonSeparated.pop();
    const prefix = colonSeparated.pop();
    const result = {
      // Allow provider without '@': "provider:prefix:name"
      provider: colonSeparated.length > 0 ? colonSeparated[0] : provider,
      prefix,
      name: name2
    };
    return validate && !validateIconName(result) ? null : result;
  }
  const name = colonSeparated[0];
  const dashSeparated = name.split("-");
  if (dashSeparated.length > 1) {
    const result = {
      provider,
      prefix: dashSeparated.shift(),
      name: dashSeparated.join("-")
    };
    return validate && !validateIconName(result) ? null : result;
  }
  if (allowSimpleName && provider === "") {
    const result = {
      provider,
      prefix: "",
      name
    };
    return validate && !validateIconName(result, allowSimpleName) ? null : result;
  }
  return null;
};
const validateIconName = (icon, allowSimpleName) => {
  if (!icon) {
    return false;
  }
  return !!// Check prefix: cannot be empty, unless allowSimpleName is enabled
  // Check name: cannot be empty
  ((allowSimpleName && icon.prefix === "" || !!icon.prefix) && !!icon.name);
};
const defaultIconDimensions = Object.freeze(
  {
    left: 0,
    top: 0,
    width: 16,
    height: 16
  }
);
const defaultIconTransformations = Object.freeze({
  rotate: 0,
  vFlip: false,
  hFlip: false
});
const defaultIconProps = Object.freeze({
  ...defaultIconDimensions,
  ...defaultIconTransformations
});
const defaultExtendedIconProps = Object.freeze({
  ...defaultIconProps,
  body: "",
  hidden: false
});
function mergeIconTransformations(obj1, obj2) {
  const result = {};
  if (!obj1.hFlip !== !obj2.hFlip) {
    result.hFlip = true;
  }
  if (!obj1.vFlip !== !obj2.vFlip) {
    result.vFlip = true;
  }
  const rotate = ((obj1.rotate || 0) + (obj2.rotate || 0)) % 4;
  if (rotate) {
    result.rotate = rotate;
  }
  return result;
}
function mergeIconData(parent, child) {
  const result = mergeIconTransformations(parent, child);
  for (const key in defaultExtendedIconProps) {
    if (key in defaultIconTransformations) {
      if (key in parent && !(key in result)) {
        result[key] = defaultIconTransformations[key];
      }
    } else if (key in child) {
      result[key] = child[key];
    } else if (key in parent) {
      result[key] = parent[key];
    }
  }
  return result;
}
function getIconsTree(data, names) {
  const icons = data.icons;
  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);
  const resolved = /* @__PURE__ */ Object.create(null);
  function resolve(name) {
    if (icons[name]) {
      return resolved[name] = [];
    }
    if (!(name in resolved)) {
      resolved[name] = null;
      const parent = aliases[name] && aliases[name].parent;
      const value = parent && resolve(parent);
      if (value) {
        resolved[name] = [parent].concat(value);
      }
    }
    return resolved[name];
  }
  Object.keys(icons).concat(Object.keys(aliases)).forEach(resolve);
  return resolved;
}
function internalGetIconData(data, name, tree) {
  const icons = data.icons;
  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);
  let currentProps = {};
  function parse(name2) {
    currentProps = mergeIconData(
      icons[name2] || aliases[name2],
      currentProps
    );
  }
  parse(name);
  tree.forEach(parse);
  return mergeIconData(data, currentProps);
}
function parseIconSet(data, callback) {
  const names = [];
  if (typeof data !== "object" || typeof data.icons !== "object") {
    return names;
  }
  if (data.not_found instanceof Array) {
    data.not_found.forEach((name) => {
      callback(name, null);
      names.push(name);
    });
  }
  const tree = getIconsTree(data);
  for (const name in tree) {
    const item = tree[name];
    if (item) {
      callback(name, internalGetIconData(data, name, item));
      names.push(name);
    }
  }
  return names;
}
const optionalPropertyDefaults = {
  provider: "",
  aliases: {},
  not_found: {},
  ...defaultIconDimensions
};
function checkOptionalProps(item, defaults) {
  for (const prop in defaults) {
    if (prop in item && typeof item[prop] !== typeof defaults[prop]) {
      return false;
    }
  }
  return true;
}
function quicklyValidateIconSet(obj) {
  if (typeof obj !== "object" || obj === null) {
    return null;
  }
  const data = obj;
  if (typeof data.prefix !== "string" || !obj.icons || typeof obj.icons !== "object") {
    return null;
  }
  if (!checkOptionalProps(obj, optionalPropertyDefaults)) {
    return null;
  }
  const icons = data.icons;
  for (const name in icons) {
    const icon = icons[name];
    if (
      // Name cannot be empty
      !name || // Must have body
      typeof icon.body !== "string" || // Check other props
      !checkOptionalProps(
        icon,
        defaultExtendedIconProps
      )
    ) {
      return null;
    }
  }
  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);
  for (const name in aliases) {
    const icon = aliases[name];
    const parent = icon.parent;
    if (
      // Name cannot be empty
      !name || // Parent must be set and point to existing icon
      typeof parent !== "string" || !icons[parent] && !aliases[parent] || // Check other props
      !checkOptionalProps(
        icon,
        defaultExtendedIconProps
      )
    ) {
      return null;
    }
  }
  return data;
}
const dataStorage = /* @__PURE__ */ Object.create(null);
function newStorage(provider, prefix) {
  return {
    provider,
    prefix,
    icons: /* @__PURE__ */ Object.create(null),
    missing: /* @__PURE__ */ new Set()
  };
}
function getStorage(provider, prefix) {
  const providerStorage = dataStorage[provider] || (dataStorage[provider] = /* @__PURE__ */ Object.create(null));
  return providerStorage[prefix] || (providerStorage[prefix] = newStorage(provider, prefix));
}
function addIconSet(storage2, data) {
  if (!quicklyValidateIconSet(data)) {
    return [];
  }
  return parseIconSet(data, (name, icon) => {
    if (icon) {
      storage2.icons[name] = icon;
    } else {
      storage2.missing.add(name);
    }
  });
}
function addIconToStorage(storage2, name, icon) {
  try {
    if (typeof icon.body === "string") {
      storage2.icons[name] = { ...icon };
      return true;
    }
  } catch (err) {
  }
  return false;
}
let simpleNames = false;
function allowSimpleNames(allow) {
  if (typeof allow === "boolean") {
    simpleNames = allow;
  }
  return simpleNames;
}
function getIconData(name) {
  const icon = typeof name === "string" ? stringToIcon(name, true, simpleNames) : name;
  if (icon) {
    const storage2 = getStorage(icon.provider, icon.prefix);
    const iconName = icon.name;
    return storage2.icons[iconName] || (storage2.missing.has(iconName) ? null : void 0);
  }
}
function addIcon(name, data) {
  const icon = stringToIcon(name, true, simpleNames);
  if (!icon) {
    return false;
  }
  const storage2 = getStorage(icon.provider, icon.prefix);
  if (data) {
    return addIconToStorage(storage2, icon.name, data);
  } else {
    storage2.missing.add(icon.name);
    return true;
  }
}
function addCollection(data, provider) {
  if (typeof data !== "object") {
    return false;
  }
  if (typeof provider !== "string") {
    provider = data.provider || "";
  }
  if (simpleNames && !provider && !data.prefix) {
    let added = false;
    if (quicklyValidateIconSet(data)) {
      data.prefix = "";
      parseIconSet(data, (name, icon) => {
        if (addIcon(name, icon)) {
          added = true;
        }
      });
    }
    return added;
  }
  const prefix = data.prefix;
  if (!validateIconName({
    prefix,
    name: "a"
  })) {
    return false;
  }
  const storage2 = getStorage(provider, prefix);
  return !!addIconSet(storage2, data);
}
const defaultIconSizeCustomisations = Object.freeze({
  width: null,
  height: null
});
const defaultIconCustomisations = Object.freeze({
  // Dimensions
  ...defaultIconSizeCustomisations,
  // Transformations
  ...defaultIconTransformations
});
const unitsSplit = /(-?[0-9.]*[0-9]+[0-9.]*)/g;
const unitsTest = /^-?[0-9.]*[0-9]+[0-9.]*$/g;
function calculateSize(size, ratio, precision) {
  if (ratio === 1) {
    return size;
  }
  precision = precision || 100;
  if (typeof size === "number") {
    return Math.ceil(size * ratio * precision) / precision;
  }
  if (typeof size !== "string") {
    return size;
  }
  const oldParts = size.split(unitsSplit);
  if (oldParts === null || !oldParts.length) {
    return size;
  }
  const newParts = [];
  let code = oldParts.shift();
  let isNumber = unitsTest.test(code);
  while (true) {
    if (isNumber) {
      const num = parseFloat(code);
      if (isNaN(num)) {
        newParts.push(code);
      } else {
        newParts.push(Math.ceil(num * ratio * precision) / precision);
      }
    } else {
      newParts.push(code);
    }
    code = oldParts.shift();
    if (code === void 0) {
      return newParts.join("");
    }
    isNumber = !isNumber;
  }
}
function splitSVGDefs(content, tag = "defs") {
  let defs = "";
  const index = content.indexOf("<" + tag);
  while (index >= 0) {
    const start = content.indexOf(">", index);
    const end = content.indexOf("</" + tag);
    if (start === -1 || end === -1) {
      break;
    }
    const endEnd = content.indexOf(">", end);
    if (endEnd === -1) {
      break;
    }
    defs += content.slice(start + 1, end).trim();
    content = content.slice(0, index).trim() + content.slice(endEnd + 1);
  }
  return {
    defs,
    content
  };
}
function mergeDefsAndContent(defs, content) {
  return defs ? "<defs>" + defs + "</defs>" + content : content;
}
function wrapSVGContent(body, start, end) {
  const split = splitSVGDefs(body);
  return mergeDefsAndContent(split.defs, start + split.content + end);
}
const isUnsetKeyword = (value) => value === "unset" || value === "undefined" || value === "none";
function iconToSVG(icon, customisations) {
  const fullIcon = {
    ...defaultIconProps,
    ...icon
  };
  const fullCustomisations = {
    ...defaultIconCustomisations,
    ...customisations
  };
  const box = {
    left: fullIcon.left,
    top: fullIcon.top,
    width: fullIcon.width,
    height: fullIcon.height
  };
  let body = fullIcon.body;
  [fullIcon, fullCustomisations].forEach((props) => {
    const transformations = [];
    const hFlip = props.hFlip;
    const vFlip = props.vFlip;
    let rotation = props.rotate;
    if (hFlip) {
      if (vFlip) {
        rotation += 2;
      } else {
        transformations.push(
          "translate(" + (box.width + box.left).toString() + " " + (0 - box.top).toString() + ")"
        );
        transformations.push("scale(-1 1)");
        box.top = box.left = 0;
      }
    } else if (vFlip) {
      transformations.push(
        "translate(" + (0 - box.left).toString() + " " + (box.height + box.top).toString() + ")"
      );
      transformations.push("scale(1 -1)");
      box.top = box.left = 0;
    }
    let tempValue;
    if (rotation < 0) {
      rotation -= Math.floor(rotation / 4) * 4;
    }
    rotation = rotation % 4;
    switch (rotation) {
      case 1:
        tempValue = box.height / 2 + box.top;
        transformations.unshift(
          "rotate(90 " + tempValue.toString() + " " + tempValue.toString() + ")"
        );
        break;
      case 2:
        transformations.unshift(
          "rotate(180 " + (box.width / 2 + box.left).toString() + " " + (box.height / 2 + box.top).toString() + ")"
        );
        break;
      case 3:
        tempValue = box.width / 2 + box.left;
        transformations.unshift(
          "rotate(-90 " + tempValue.toString() + " " + tempValue.toString() + ")"
        );
        break;
    }
    if (rotation % 2 === 1) {
      if (box.left !== box.top) {
        tempValue = box.left;
        box.left = box.top;
        box.top = tempValue;
      }
      if (box.width !== box.height) {
        tempValue = box.width;
        box.width = box.height;
        box.height = tempValue;
      }
    }
    if (transformations.length) {
      body = wrapSVGContent(
        body,
        '<g transform="' + transformations.join(" ") + '">',
        "</g>"
      );
    }
  });
  const customisationsWidth = fullCustomisations.width;
  const customisationsHeight = fullCustomisations.height;
  const boxWidth = box.width;
  const boxHeight = box.height;
  let width;
  let height;
  if (customisationsWidth === null) {
    height = customisationsHeight === null ? "1em" : customisationsHeight === "auto" ? boxHeight : customisationsHeight;
    width = calculateSize(height, boxWidth / boxHeight);
  } else {
    width = customisationsWidth === "auto" ? boxWidth : customisationsWidth;
    height = customisationsHeight === null ? calculateSize(width, boxHeight / boxWidth) : customisationsHeight === "auto" ? boxHeight : customisationsHeight;
  }
  const attributes = {};
  const setAttr = (prop, value) => {
    if (!isUnsetKeyword(value)) {
      attributes[prop] = value.toString();
    }
  };
  setAttr("width", width);
  setAttr("height", height);
  const viewBox = [box.left, box.top, boxWidth, boxHeight];
  attributes.viewBox = viewBox.join(" ");
  return {
    attributes,
    viewBox,
    body
  };
}
const regex = /\sid="(\S+)"/g;
const randomPrefix = "IconifyId" + Date.now().toString(16) + (Math.random() * 16777216 | 0).toString(16);
let counter = 0;
function replaceIDs(body, prefix = randomPrefix) {
  const ids = [];
  let match;
  while (match = regex.exec(body)) {
    ids.push(match[1]);
  }
  if (!ids.length) {
    return body;
  }
  const suffix = "suffix" + (Math.random() * 16777216 | Date.now()).toString(16);
  ids.forEach((id) => {
    const newID = typeof prefix === "function" ? prefix(id) : prefix + (counter++).toString();
    const escapedID = id.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    body = body.replace(
      // Allowed characters before id: [#;"]
      // Allowed characters after id: [)"], .[a-z]
      new RegExp('([#;"])(' + escapedID + ')([")]|\\.[a-z])', "g"),
      "$1" + newID + suffix + "$3"
    );
  });
  body = body.replace(new RegExp(suffix, "g"), "");
  return body;
}
const storage = /* @__PURE__ */ Object.create(null);
function setAPIModule(provider, item) {
  storage[provider] = item;
}
function getAPIModule(provider) {
  return storage[provider] || storage[""];
}
function createAPIConfig(source) {
  let resources;
  if (typeof source.resources === "string") {
    resources = [source.resources];
  } else {
    resources = source.resources;
    if (!(resources instanceof Array) || !resources.length) {
      return null;
    }
  }
  const result = {
    // API hosts
    resources,
    // Root path
    path: source.path || "/",
    // URL length limit
    maxURL: source.maxURL || 500,
    // Timeout before next host is used.
    rotate: source.rotate || 750,
    // Timeout before failing query.
    timeout: source.timeout || 5e3,
    // Randomise default API end point.
    random: source.random === true,
    // Start index
    index: source.index || 0,
    // Receive data after time out (used if time out kicks in first, then API module sends data anyway).
    dataAfterTimeout: source.dataAfterTimeout !== false
  };
  return result;
}
const configStorage = /* @__PURE__ */ Object.create(null);
const fallBackAPISources = [
  "https://api.simplesvg.com",
  "https://api.unisvg.com"
];
const fallBackAPI = [];
while (fallBackAPISources.length > 0) {
  if (fallBackAPISources.length === 1) {
    fallBackAPI.push(fallBackAPISources.shift());
  } else {
    if (Math.random() > 0.5) {
      fallBackAPI.push(fallBackAPISources.shift());
    } else {
      fallBackAPI.push(fallBackAPISources.pop());
    }
  }
}
configStorage[""] = createAPIConfig({
  resources: ["https://api.iconify.design"].concat(fallBackAPI)
});
function addAPIProvider(provider, customConfig) {
  const config = createAPIConfig(customConfig);
  if (config === null) {
    return false;
  }
  configStorage[provider] = config;
  return true;
}
function getAPIConfig(provider) {
  return configStorage[provider];
}
const detectFetch = () => {
  let callback;
  try {
    callback = fetch;
    if (typeof callback === "function") {
      return callback;
    }
  } catch (err) {
  }
};
let fetchModule = detectFetch();
function calculateMaxLength(provider, prefix) {
  const config = getAPIConfig(provider);
  if (!config) {
    return 0;
  }
  let result;
  if (!config.maxURL) {
    result = 0;
  } else {
    let maxHostLength = 0;
    config.resources.forEach((item) => {
      const host = item;
      maxHostLength = Math.max(maxHostLength, host.length);
    });
    const url = prefix + ".json?icons=";
    result = config.maxURL - maxHostLength - config.path.length - url.length;
  }
  return result;
}
function shouldAbort(status) {
  return status === 404;
}
const prepare = (provider, prefix, icons) => {
  const results = [];
  const maxLength = calculateMaxLength(provider, prefix);
  const type = "icons";
  let item = {
    type,
    provider,
    prefix,
    icons: []
  };
  let length = 0;
  icons.forEach((name, index) => {
    length += name.length + 1;
    if (length >= maxLength && index > 0) {
      results.push(item);
      item = {
        type,
        provider,
        prefix,
        icons: []
      };
      length = name.length;
    }
    item.icons.push(name);
  });
  results.push(item);
  return results;
};
function getPath(provider) {
  if (typeof provider === "string") {
    const config = getAPIConfig(provider);
    if (config) {
      return config.path;
    }
  }
  return "/";
}
const send = (host, params, callback) => {
  if (!fetchModule) {
    callback("abort", 424);
    return;
  }
  let path = getPath(params.provider);
  switch (params.type) {
    case "icons": {
      const prefix = params.prefix;
      const icons = params.icons;
      const iconsList = icons.join(",");
      const urlParams = new URLSearchParams({
        icons: iconsList
      });
      path += prefix + ".json?" + urlParams.toString();
      break;
    }
    case "custom": {
      const uri = params.uri;
      path += uri.slice(0, 1) === "/" ? uri.slice(1) : uri;
      break;
    }
    default:
      callback("abort", 400);
      return;
  }
  let defaultError = 503;
  fetchModule(host + path).then((response) => {
    const status = response.status;
    if (status !== 200) {
      setTimeout(() => {
        callback(shouldAbort(status) ? "abort" : "next", status);
      });
      return;
    }
    defaultError = 501;
    return response.json();
  }).then((data) => {
    if (typeof data !== "object" || data === null) {
      setTimeout(() => {
        if (data === 404) {
          callback("abort", data);
        } else {
          callback("next", defaultError);
        }
      });
      return;
    }
    setTimeout(() => {
      callback("success", data);
    });
  }).catch(() => {
    callback("next", defaultError);
  });
};
const fetchAPIModule = {
  prepare,
  send
};
function sortIcons(icons) {
  const result = {
    loaded: [],
    missing: [],
    pending: []
  };
  const storage2 = /* @__PURE__ */ Object.create(null);
  icons.sort((a, b) => {
    if (a.provider !== b.provider) {
      return a.provider.localeCompare(b.provider);
    }
    if (a.prefix !== b.prefix) {
      return a.prefix.localeCompare(b.prefix);
    }
    return a.name.localeCompare(b.name);
  });
  let lastIcon = {
    provider: "",
    prefix: "",
    name: ""
  };
  icons.forEach((icon) => {
    if (lastIcon.name === icon.name && lastIcon.prefix === icon.prefix && lastIcon.provider === icon.provider) {
      return;
    }
    lastIcon = icon;
    const provider = icon.provider;
    const prefix = icon.prefix;
    const name = icon.name;
    const providerStorage = storage2[provider] || (storage2[provider] = /* @__PURE__ */ Object.create(null));
    const localStorage = providerStorage[prefix] || (providerStorage[prefix] = getStorage(provider, prefix));
    let list;
    if (name in localStorage.icons) {
      list = result.loaded;
    } else if (prefix === "" || localStorage.missing.has(name)) {
      list = result.missing;
    } else {
      list = result.pending;
    }
    const item = {
      provider,
      prefix,
      name
    };
    list.push(item);
  });
  return result;
}
function removeCallback(storages, id) {
  storages.forEach((storage2) => {
    const items = storage2.loaderCallbacks;
    if (items) {
      storage2.loaderCallbacks = items.filter((row) => row.id !== id);
    }
  });
}
function updateCallbacks(storage2) {
  if (!storage2.pendingCallbacksFlag) {
    storage2.pendingCallbacksFlag = true;
    setTimeout(() => {
      storage2.pendingCallbacksFlag = false;
      const items = storage2.loaderCallbacks ? storage2.loaderCallbacks.slice(0) : [];
      if (!items.length) {
        return;
      }
      let hasPending = false;
      const provider = storage2.provider;
      const prefix = storage2.prefix;
      items.forEach((item) => {
        const icons = item.icons;
        const oldLength = icons.pending.length;
        icons.pending = icons.pending.filter((icon) => {
          if (icon.prefix !== prefix) {
            return true;
          }
          const name = icon.name;
          if (storage2.icons[name]) {
            icons.loaded.push({
              provider,
              prefix,
              name
            });
          } else if (storage2.missing.has(name)) {
            icons.missing.push({
              provider,
              prefix,
              name
            });
          } else {
            hasPending = true;
            return true;
          }
          return false;
        });
        if (icons.pending.length !== oldLength) {
          if (!hasPending) {
            removeCallback([storage2], item.id);
          }
          item.callback(
            icons.loaded.slice(0),
            icons.missing.slice(0),
            icons.pending.slice(0),
            item.abort
          );
        }
      });
    });
  }
}
let idCounter = 0;
function storeCallback(callback, icons, pendingSources) {
  const id = idCounter++;
  const abort = removeCallback.bind(null, pendingSources, id);
  if (!icons.pending.length) {
    return abort;
  }
  const item = {
    id,
    icons,
    callback,
    abort
  };
  pendingSources.forEach((storage2) => {
    (storage2.loaderCallbacks || (storage2.loaderCallbacks = [])).push(item);
  });
  return abort;
}
function listToIcons(list, validate = true, simpleNames2 = false) {
  const result = [];
  list.forEach((item) => {
    const icon = typeof item === "string" ? stringToIcon(item, validate, simpleNames2) : item;
    if (icon) {
      result.push(icon);
    }
  });
  return result;
}
var defaultConfig = {
  resources: [],
  index: 0,
  timeout: 2e3,
  rotate: 750,
  random: false,
  dataAfterTimeout: false
};
function sendQuery(config, payload, query, done) {
  const resourcesCount = config.resources.length;
  const startIndex = config.random ? Math.floor(Math.random() * resourcesCount) : config.index;
  let resources;
  if (config.random) {
    let list = config.resources.slice(0);
    resources = [];
    while (list.length > 1) {
      const nextIndex = Math.floor(Math.random() * list.length);
      resources.push(list[nextIndex]);
      list = list.slice(0, nextIndex).concat(list.slice(nextIndex + 1));
    }
    resources = resources.concat(list);
  } else {
    resources = config.resources.slice(startIndex).concat(config.resources.slice(0, startIndex));
  }
  const startTime = Date.now();
  let status = "pending";
  let queriesSent = 0;
  let lastError;
  let timer = null;
  let queue = [];
  let doneCallbacks = [];
  if (typeof done === "function") {
    doneCallbacks.push(done);
  }
  function resetTimer() {
    if (timer) {
      clearTimeout(timer);
      timer = null;
    }
  }
  function abort() {
    if (status === "pending") {
      status = "aborted";
    }
    resetTimer();
    queue.forEach((item) => {
      if (item.status === "pending") {
        item.status = "aborted";
      }
    });
    queue = [];
  }
  function subscribe(callback, overwrite) {
    if (overwrite) {
      doneCallbacks = [];
    }
    if (typeof callback === "function") {
      doneCallbacks.push(callback);
    }
  }
  function getQueryStatus() {
    return {
      startTime,
      payload,
      status,
      queriesSent,
      queriesPending: queue.length,
      subscribe,
      abort
    };
  }
  function failQuery() {
    status = "failed";
    doneCallbacks.forEach((callback) => {
      callback(void 0, lastError);
    });
  }
  function clearQueue() {
    queue.forEach((item) => {
      if (item.status === "pending") {
        item.status = "aborted";
      }
    });
    queue = [];
  }
  function moduleResponse(item, response, data) {
    const isError = response !== "success";
    queue = queue.filter((queued) => queued !== item);
    switch (status) {
      case "pending":
        break;
      case "failed":
        if (isError || !config.dataAfterTimeout) {
          return;
        }
        break;
      default:
        return;
    }
    if (response === "abort") {
      lastError = data;
      failQuery();
      return;
    }
    if (isError) {
      lastError = data;
      if (!queue.length) {
        if (!resources.length) {
          failQuery();
        } else {
          execNext();
        }
      }
      return;
    }
    resetTimer();
    clearQueue();
    if (!config.random) {
      const index = config.resources.indexOf(item.resource);
      if (index !== -1 && index !== config.index) {
        config.index = index;
      }
    }
    status = "completed";
    doneCallbacks.forEach((callback) => {
      callback(data);
    });
  }
  function execNext() {
    if (status !== "pending") {
      return;
    }
    resetTimer();
    const resource = resources.shift();
    if (resource === void 0) {
      if (queue.length) {
        timer = setTimeout(() => {
          resetTimer();
          if (status === "pending") {
            clearQueue();
            failQuery();
          }
        }, config.timeout);
        return;
      }
      failQuery();
      return;
    }
    const item = {
      status: "pending",
      resource,
      callback: (status2, data) => {
        moduleResponse(item, status2, data);
      }
    };
    queue.push(item);
    queriesSent++;
    timer = setTimeout(execNext, config.rotate);
    query(resource, payload, item.callback);
  }
  setTimeout(execNext);
  return getQueryStatus;
}
function initRedundancy(cfg) {
  const config = {
    ...defaultConfig,
    ...cfg
  };
  let queries = [];
  function cleanup() {
    queries = queries.filter((item) => item().status === "pending");
  }
  function query(payload, queryCallback, doneCallback) {
    const query2 = sendQuery(
      config,
      payload,
      queryCallback,
      (data, error) => {
        cleanup();
        if (doneCallback) {
          doneCallback(data, error);
        }
      }
    );
    queries.push(query2);
    return query2;
  }
  function find(callback) {
    return queries.find((value) => {
      return callback(value);
    }) || null;
  }
  const instance = {
    query,
    find,
    setIndex: (index) => {
      config.index = index;
    },
    getIndex: () => config.index,
    cleanup
  };
  return instance;
}
function emptyCallback$1() {
}
const redundancyCache = /* @__PURE__ */ Object.create(null);
function getRedundancyCache(provider) {
  if (!redundancyCache[provider]) {
    const config = getAPIConfig(provider);
    if (!config) {
      return;
    }
    const redundancy = initRedundancy(config);
    const cachedReundancy = {
      config,
      redundancy
    };
    redundancyCache[provider] = cachedReundancy;
  }
  return redundancyCache[provider];
}
function sendAPIQuery(target, query, callback) {
  let redundancy;
  let send2;
  if (typeof target === "string") {
    const api = getAPIModule(target);
    if (!api) {
      callback(void 0, 424);
      return emptyCallback$1;
    }
    send2 = api.send;
    const cached = getRedundancyCache(target);
    if (cached) {
      redundancy = cached.redundancy;
    }
  } else {
    const config = createAPIConfig(target);
    if (config) {
      redundancy = initRedundancy(config);
      const moduleKey = target.resources ? target.resources[0] : "";
      const api = getAPIModule(moduleKey);
      if (api) {
        send2 = api.send;
      }
    }
  }
  if (!redundancy || !send2) {
    callback(void 0, 424);
    return emptyCallback$1;
  }
  return redundancy.query(query, send2, callback)().abort;
}
function emptyCallback() {
}
function loadedNewIcons(storage2) {
  if (!storage2.iconsLoaderFlag) {
    storage2.iconsLoaderFlag = true;
    setTimeout(() => {
      storage2.iconsLoaderFlag = false;
      updateCallbacks(storage2);
    });
  }
}
function checkIconNamesForAPI(icons) {
  const valid = [];
  const invalid = [];
  icons.forEach((name) => {
    (name.match(matchIconName) ? valid : invalid).push(name);
  });
  return {
    valid,
    invalid
  };
}
function parseLoaderResponse(storage2, icons, data) {
  function checkMissing() {
    const pending = storage2.pendingIcons;
    icons.forEach((name) => {
      if (pending) {
        pending.delete(name);
      }
      if (!storage2.icons[name]) {
        storage2.missing.add(name);
      }
    });
  }
  if (data && typeof data === "object") {
    try {
      const parsed = addIconSet(storage2, data);
      if (!parsed.length) {
        checkMissing();
        return;
      }
    } catch (err) {
      console.error(err);
    }
  }
  checkMissing();
  loadedNewIcons(storage2);
}
function parsePossiblyAsyncResponse(response, callback) {
  if (response instanceof Promise) {
    response.then((data) => {
      callback(data);
    }).catch(() => {
      callback(null);
    });
  } else {
    callback(response);
  }
}
function loadNewIcons(storage2, icons) {
  if (!storage2.iconsToLoad) {
    storage2.iconsToLoad = icons;
  } else {
    storage2.iconsToLoad = storage2.iconsToLoad.concat(icons).sort();
  }
  if (!storage2.iconsQueueFlag) {
    storage2.iconsQueueFlag = true;
    setTimeout(() => {
      storage2.iconsQueueFlag = false;
      const { provider, prefix } = storage2;
      const icons2 = storage2.iconsToLoad;
      delete storage2.iconsToLoad;
      if (!icons2 || !icons2.length) {
        return;
      }
      const customIconLoader = storage2.loadIcon;
      if (storage2.loadIcons && (icons2.length > 1 || !customIconLoader)) {
        parsePossiblyAsyncResponse(
          storage2.loadIcons(icons2, prefix, provider),
          (data) => {
            parseLoaderResponse(storage2, icons2, data);
          }
        );
        return;
      }
      if (customIconLoader) {
        icons2.forEach((name) => {
          const response = customIconLoader(name, prefix, provider);
          parsePossiblyAsyncResponse(response, (data) => {
            const iconSet = data ? {
              prefix,
              icons: {
                [name]: data
              }
            } : null;
            parseLoaderResponse(storage2, [name], iconSet);
          });
        });
        return;
      }
      const { valid, invalid } = checkIconNamesForAPI(icons2);
      if (invalid.length) {
        parseLoaderResponse(storage2, invalid, null);
      }
      if (!valid.length) {
        return;
      }
      const api = prefix.match(matchIconName) ? getAPIModule(provider) : null;
      if (!api) {
        parseLoaderResponse(storage2, valid, null);
        return;
      }
      const params = api.prepare(provider, prefix, valid);
      params.forEach((item) => {
        sendAPIQuery(provider, item, (data) => {
          parseLoaderResponse(storage2, item.icons, data);
        });
      });
    });
  }
}
const loadIcons = (icons, callback) => {
  const cleanedIcons = listToIcons(icons, true, allowSimpleNames());
  const sortedIcons = sortIcons(cleanedIcons);
  if (!sortedIcons.pending.length) {
    let callCallback = true;
    if (callback) {
      setTimeout(() => {
        if (callCallback) {
          callback(
            sortedIcons.loaded,
            sortedIcons.missing,
            sortedIcons.pending,
            emptyCallback
          );
        }
      });
    }
    return () => {
      callCallback = false;
    };
  }
  const newIcons = /* @__PURE__ */ Object.create(null);
  const sources = [];
  let lastProvider, lastPrefix;
  sortedIcons.pending.forEach((icon) => {
    const { provider, prefix } = icon;
    if (prefix === lastPrefix && provider === lastProvider) {
      return;
    }
    lastProvider = provider;
    lastPrefix = prefix;
    sources.push(getStorage(provider, prefix));
    const providerNewIcons = newIcons[provider] || (newIcons[provider] = /* @__PURE__ */ Object.create(null));
    if (!providerNewIcons[prefix]) {
      providerNewIcons[prefix] = [];
    }
  });
  sortedIcons.pending.forEach((icon) => {
    const { provider, prefix, name } = icon;
    const storage2 = getStorage(provider, prefix);
    const pendingQueue = storage2.pendingIcons || (storage2.pendingIcons = /* @__PURE__ */ new Set());
    if (!pendingQueue.has(name)) {
      pendingQueue.add(name);
      newIcons[provider][prefix].push(name);
    }
  });
  sources.forEach((storage2) => {
    const list = newIcons[storage2.provider][storage2.prefix];
    if (list.length) {
      loadNewIcons(storage2, list);
    }
  });
  return callback ? storeCallback(callback, sortedIcons, sources) : emptyCallback;
};
function mergeCustomisations(defaults, item) {
  const result = {
    ...defaults
  };
  for (const key in item) {
    const value = item[key];
    const valueType = typeof value;
    if (key in defaultIconSizeCustomisations) {
      if (value === null || value && (valueType === "string" || valueType === "number")) {
        result[key] = value;
      }
    } else if (valueType === typeof result[key]) {
      result[key] = key === "rotate" ? value % 4 : value;
    }
  }
  return result;
}
const separator = /[\s,]+/;
function flipFromString(custom, flip) {
  flip.split(separator).forEach((str) => {
    const value = str.trim();
    switch (value) {
      case "horizontal":
        custom.hFlip = true;
        break;
      case "vertical":
        custom.vFlip = true;
        break;
    }
  });
}
function rotateFromString(value, defaultValue = 0) {
  const units = value.replace(/^-?[0-9.]*/, "");
  function cleanup(value2) {
    while (value2 < 0) {
      value2 += 4;
    }
    return value2 % 4;
  }
  if (units === "") {
    const num = parseInt(value);
    return isNaN(num) ? 0 : cleanup(num);
  } else if (units !== value) {
    let split = 0;
    switch (units) {
      case "%":
        split = 25;
        break;
      case "deg":
        split = 90;
    }
    if (split) {
      let num = parseFloat(value.slice(0, value.length - units.length));
      if (isNaN(num)) {
        return 0;
      }
      num = num / split;
      return num % 1 === 0 ? cleanup(num) : 0;
    }
  }
  return defaultValue;
}
function iconToHTML(body, attributes) {
  let renderAttribsHTML = body.indexOf("xlink:") === -1 ? "" : ' xmlns:xlink="http://www.w3.org/1999/xlink"';
  for (const attr2 in attributes) {
    renderAttribsHTML += " " + attr2 + '="' + attributes[attr2] + '"';
  }
  return '<svg xmlns="http://www.w3.org/2000/svg"' + renderAttribsHTML + ">" + body + "</svg>";
}
function encodeSVGforURL(svg) {
  return svg.replace(/"/g, "'").replace(/%/g, "%25").replace(/#/g, "%23").replace(/</g, "%3C").replace(/>/g, "%3E").replace(/\s+/g, " ");
}
function svgToData(svg) {
  return "data:image/svg+xml," + encodeSVGforURL(svg);
}
function svgToURL(svg) {
  return 'url("' + svgToData(svg) + '")';
}
const defaultExtendedIconCustomisations = {
  ...defaultIconCustomisations,
  inline: false
};
const svgDefaults = {
  "xmlns": "http://www.w3.org/2000/svg",
  "xmlns:xlink": "http://www.w3.org/1999/xlink",
  "aria-hidden": true,
  "role": "img"
};
const commonProps = {
  display: "inline-block"
};
const monotoneProps = {
  "background-color": "currentColor"
};
const coloredProps = {
  "background-color": "transparent"
};
const propsToAdd = {
  image: "var(--svg)",
  repeat: "no-repeat",
  size: "100% 100%"
};
const propsToAddTo = {
  "-webkit-mask": monotoneProps,
  "mask": monotoneProps,
  "background": coloredProps
};
for (const prefix in propsToAddTo) {
  const list = propsToAddTo[prefix];
  for (const prop in propsToAdd) {
    list[prefix + "-" + prop] = propsToAdd[prop];
  }
}
function fixSize(value) {
  return value + (value.match(/^[-0-9.]+$/) ? "px" : "");
}
function render(icon, props) {
  const customisations = mergeCustomisations(defaultExtendedIconCustomisations, props);
  const mode = props.mode || "svg";
  const componentProps = mode === "svg" ? { ...svgDefaults } : {};
  if (icon.body.indexOf("xlink:") === -1) {
    delete componentProps["xmlns:xlink"];
  }
  let style = typeof props.style === "string" ? props.style : "";
  for (let key in props) {
    const value = props[key];
    if (value === void 0) {
      continue;
    }
    switch (key) {
      // Properties to ignore
      case "icon":
      case "style":
      case "onLoad":
      case "mode":
      case "ssr":
        break;
      // Boolean attributes
      case "inline":
      case "hFlip":
      case "vFlip":
        customisations[key] = value === true || value === "true" || value === 1;
        break;
      // Flip as string: 'horizontal,vertical'
      case "flip":
        if (typeof value === "string") {
          flipFromString(customisations, value);
        }
        break;
      // Color: copy to style, add extra ';' in case style is missing it
      case "color":
        style = style + (style.length > 0 && style.trim().slice(-1) !== ";" ? ";" : "") + "color: " + value + "; ";
        break;
      // Rotation as string
      case "rotate":
        if (typeof value === "string") {
          customisations[key] = rotateFromString(value);
        } else if (typeof value === "number") {
          customisations[key] = value;
        }
        break;
      // Remove aria-hidden
      case "ariaHidden":
      case "aria-hidden":
        if (value !== true && value !== "true") {
          delete componentProps["aria-hidden"];
        }
        break;
      default:
        if (key.slice(0, 3) === "on:") {
          break;
        }
        if (defaultExtendedIconCustomisations[key] === void 0) {
          componentProps[key] = value;
        }
    }
  }
  const item = iconToSVG(icon, customisations);
  const renderAttribs = item.attributes;
  if (customisations.inline) {
    style = "vertical-align: -0.125em; " + style;
  }
  if (mode === "svg") {
    Object.assign(componentProps, renderAttribs);
    if (style !== "") {
      componentProps.style = style;
    }
    let localCounter = 0;
    let id = props.id;
    if (typeof id === "string") {
      id = id.replace(/-/g, "_");
    }
    return {
      svg: true,
      attributes: componentProps,
      body: replaceIDs(item.body, id ? () => id + "ID" + localCounter++ : "iconifySvelte")
    };
  }
  const { body, width, height } = icon;
  const useMask = mode === "mask" || (mode === "bg" ? false : body.indexOf("currentColor") !== -1);
  const html2 = iconToHTML(body, {
    ...renderAttribs,
    width: width + "",
    height: height + ""
  });
  const url = svgToURL(html2);
  const styles = {
    "--svg": url
  };
  const size = (prop) => {
    const value = renderAttribs[prop];
    if (value) {
      styles[prop] = fixSize(value);
    }
  };
  size("width");
  size("height");
  Object.assign(styles, commonProps, useMask ? monotoneProps : coloredProps);
  let customStyle = "";
  for (const key in styles) {
    customStyle += key + ": " + styles[key] + ";";
  }
  componentProps.style = customStyle + style;
  return {
    svg: false,
    attributes: componentProps
  };
}
allowSimpleNames(true);
setAPIModule("", fetchAPIModule);
if (typeof document !== "undefined" && typeof window !== "undefined") {
  const _window = window;
  if (_window.IconifyPreload !== void 0) {
    const preload = _window.IconifyPreload;
    const err = "Invalid IconifyPreload syntax.";
    if (typeof preload === "object" && preload !== null) {
      (preload instanceof Array ? preload : [preload]).forEach((item) => {
        try {
          if (
            // Check if item is an object and not null/array
            typeof item !== "object" || item === null || item instanceof Array || // Check for 'icons' and 'prefix'
            typeof item.icons !== "object" || typeof item.prefix !== "string" || // Add icon set
            !addCollection(item)
          ) {
            console.error(err);
          }
        } catch (e) {
          console.error(err);
        }
      });
    }
  }
  if (_window.IconifyProviders !== void 0) {
    const providers = _window.IconifyProviders;
    if (typeof providers === "object" && providers !== null) {
      for (let key in providers) {
        const err = "IconifyProviders[" + key + "] is invalid.";
        try {
          const value = providers[key];
          if (typeof value !== "object" || !value || value.resources === void 0) {
            continue;
          }
          if (!addAPIProvider(key, value)) {
            console.error(err);
          }
        } catch (e) {
          console.error(err);
        }
      }
    }
  }
}
function checkIconState(icon, state, mounted, callback, onload) {
  function abortLoading() {
    if (state.loading) {
      state.loading.abort();
      state.loading = null;
    }
  }
  if (typeof icon === "object" && icon !== null && typeof icon.body === "string") {
    state.name = "";
    abortLoading();
    return { data: { ...defaultIconProps, ...icon } };
  }
  let iconName;
  if (typeof icon !== "string" || (iconName = stringToIcon(icon, false, true)) === null) {
    abortLoading();
    return null;
  }
  const data = getIconData(iconName);
  if (!data) {
    if (mounted && (!state.loading || state.loading.name !== icon)) {
      abortLoading();
      state.name = "";
      state.loading = {
        name: icon,
        abort: loadIcons([iconName], callback)
      };
    }
    return null;
  }
  abortLoading();
  if (state.name !== icon) {
    state.name = icon;
    if (onload && !state.destroyed) {
      setTimeout(() => {
        onload(icon);
      });
    }
  }
  const classes = ["iconify"];
  if (iconName.prefix !== "") {
    classes.push("iconify--" + iconName.prefix);
  }
  if (iconName.provider !== "") {
    classes.push("iconify--" + iconName.provider);
  }
  return { data, classes };
}
function generateIcon(icon, props) {
  return icon ? render({
    ...defaultIconProps,
    ...icon
  }, props) : null;
}
function Icon($$payload, $$props) {
  push();
  const iconState = {
    // Last icon name
    name: "",
    // Loading status
    loading: null,
    // Destroyed status
    destroyed: false
  };
  const { $$slots, $$events, ...props } = $$props;
  let mounted = false;
  let isMounted = !!props.ssr || mounted;
  let iconData = (() => {
    return checkIconState(props.icon, iconState, isMounted, loaded, props.onload);
  })();
  let data = (() => {
    const generatedData = iconData ? generateIcon(iconData.data, props) : null;
    if (generatedData && iconData.classes) {
      generatedData.attributes["class"] = (typeof props["class"] === "string" ? props["class"] + " " : "") + iconData.classes.join(" ");
    }
    return generatedData;
  })();
  function loaded() {
  }
  onDestroy(() => {
    iconState.destroyed = true;
    if (iconState.loading) {
      iconState.loading.abort();
      iconState.loading = null;
    }
  });
  if (data) {
    $$payload.out.push("<!--[-->");
    if (data.svg) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<svg${spread_attributes({ ...data.attributes }, null, void 0, void 0, 3)}>${html(data.body)}</svg>`);
    } else {
      $$payload.out.push("<!--[!-->");
      $$payload.out.push(`<span${spread_attributes({ ...data.attributes })}></span>`);
    }
    $$payload.out.push(`<!--]-->`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]-->`);
  pop();
}
function GameHUD($$payload, $$props) {
  let {
    score,
    time,
    totalTime,
    lives,
    maxLives
  } = $$props;
  const each_array = ensure_array_like(
    // export let score: number;
    // export let time: number;
    // export let lives: number;
    // export let maxLives: number;
    // export let opponentScore: number | null = null;
    // export let showOpponent: boolean = false;
    Array(maxLives)
  );
  $$payload.out.push(`<div class="fixed top-[3vh] left-0 right-0 z-10 px-[4vw] py-[3vh] flex flex-col items-center justify-around gap-[1vh] text-white"><div class="relative w-full flex items-center gap-2 bg-black/20"><div class="absolute left-0 top-1/2 -translate-y-1/2 w-[6vh] h-[6vh] flex items-center justify-center p-1 rounded-full border-4 border-cyan-400 bg-gray-800 z-10">`);
  Icon($$payload, {
    height: "3vh",
    color: "white",
    icon: "material-symbols:timer"
  });
  $$payload.out.push(`<!----></div> <div class="absolute right-0 top-1/2 -translate-y-1/2 w-[6vh] h-[6vh] flex items-center justify-center p-1 rounded-full border-4 border-cyan-400 bg-gray-800 z-10 font-medium text-[2.5vh]">${escape_html(time)}s</div> <div class="relative w-full h-6 rounded-xl overflow-hidden"><div class="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-cyan-400 to-purple-600 transition-all duration-1000 ease-linear"${attr_style(`width: ${stringify(time / totalTime * 100)}%;`)}></div></div></div> <div class="flex gap-1"><!--[-->`);
  for (let i = 0, $$length = each_array.length; i < $$length; i++) {
    each_array[i];
    if (i < lives) {
      $$payload.out.push("<!--[-->");
      Icon($$payload, {
        height: "4vh",
        color: "red",
        icon: "material-symbols:favorite"
      });
    } else {
      $$payload.out.push("<!--[!-->");
      Icon($$payload, {
        height: "4vh",
        color: "red",
        icon: "material-symbols:favorite-outline"
      });
    }
    $$payload.out.push(`<!--]-->`);
  }
  $$payload.out.push(`<!--]--></div> <div class="flex flex-col items-center"><span class="text-[2vh] font-medium">Total Point</span> <div class="font-bold text-[6vh]">${escape_html(score)}</div></div></div>`);
}
function Countdown($$payload, $$props) {
  push();
  let { duration = 3, show = false } = $$props;
  let currentCount = duration;
  if (show && currentCount >= 0) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="fixed inset-0 bg-black/60 flex justify-center items-center z-[2000] svelte-vzg9ol"><img class="counter svelte-vzg9ol"${attr("src", `/assets/images/counter/${stringify(currentCount === 0 ? "GO" : currentCount)}.svg`)} alt=""/></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]-->`);
  pop();
}
function EndGame($$payload, $$props) {
  let { show = false, finalScore = 0 } = $$props;
  if (show) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="game-end-overlay svelte-1os0kc9"><div class="game-background svelte-1os0kc9"></div> <div class="game-panel svelte-1os0kc9"><div class="panel-blur-bg svelte-1os0kc9"></div> <div class="panel-content svelte-1os0kc9"><div class="game-over-title svelte-1os0kc9"><h1 class="game-over-text svelte-1os0kc9">GAME OVER</h1></div> <div class="score-section svelte-1os0kc9"><div class="score-label svelte-1os0kc9">SCORE</div> <div class="score-value svelte-1os0kc9">${escape_html(finalScore)}</div></div> <button class="back-to-lobby-btn svelte-1os0kc9"><div class="btn-border"></div> <div class="btn-background"></div> <span class="btn-text">BACK TO LOBBY</span></button></div></div></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]-->`);
}
function _page($$payload, $$props) {
  push();
  var $$store_subs;
  let showCountdown = false;
  const gameId = page.params.id;
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>TicTaps - ${escape_html(gameId)}</title>`;
  });
  Preloading($$payload, {
    progress: store_get($$store_subs ??= {}, "$gameState", gameState).loadingProgress
  });
  $$payload.out.push(`<!----> `);
  if (!store_get($$store_subs ??= {}, "$gameState", gameState).isCountdown && !store_get($$store_subs ??= {}, "$gameState", gameState).isLoading) {
    $$payload.out.push("<!--[-->");
    StartScreen($$payload, { gameId });
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> <div class="w-screen h-screen overflow-hidden relative">`);
  if (store_get($$store_subs ??= {}, "$gameState", gameState).isPlaying) {
    $$payload.out.push("<!--[-->");
    GameHUD($$payload, {
      score: store_get($$store_subs ??= {}, "$gameState", gameState).score,
      time: store_get($$store_subs ??= {}, "$gameState", gameState).time,
      totalTime: store_get($$store_subs ??= {}, "$gameState", gameState).totalTime,
      lives: store_get($$store_subs ??= {}, "$gameState", gameState).lives,
      maxLives: store_get($$store_subs ??= {}, "$gameState", gameState).maxLives,
      opponentScore: store_get($$store_subs ??= {}, "$gameState", gameState).opponentScore,
      showOpponent: store_get($$store_subs ??= {}, "$gameState", gameState).opponentScore !== null
    });
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> <div class="w-full h-full box-border" id="game-container"></div> `);
  Countdown($$payload, { show: showCountdown, duration: 3 });
  $$payload.out.push(`<!----> `);
  EndGame($$payload, {
    show: store_get($$store_subs ??= {}, "$gameState", gameState).gameOver,
    finalScore: store_get($$store_subs ??= {}, "$gameState", gameState).score
  });
  $$payload.out.push(`<!----></div>`);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
export {
  _page as default
};
