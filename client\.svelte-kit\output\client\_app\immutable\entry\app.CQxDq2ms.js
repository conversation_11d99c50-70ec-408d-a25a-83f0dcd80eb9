const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["../nodes/0.B4C_3hdl.js","../chunks/DsnmJJEf.js","../chunks/UXlYzocm.js","../chunks/DdHBXBi3.js","../assets/0.SeeHXBop.css","../nodes/1.um0fz6Fw.js","../chunks/BzjXworP.js","../chunks/AD_Ai2mn.js","../chunks/ByeBDOvZ.js","../chunks/CcjTyetE.js","../chunks/D8RLQ4bx.js","../nodes/2.CMUzneym.js","../chunks/DLbaRTQ9.js","../chunks/nPv1uoDA.js","../assets/2.Cz6zzJk0.css","../nodes/3.uFkFLWck.js","../chunks/COKU4n9m.js","../assets/3.M6-0dyh5.css"])))=>i.map(i=>d[i]);
import{h as N,a as G,b as U,E as W,m as Y,f as J,o as K,p as Q,i as X,u as z,Y as k,ab as H,B as m,ai as Z,W as $,X as ee,F as te,w as re,x as se,aD as x,ao as ae,G as I,I as R,O as ne,K as b,L as oe,Q as O,M as ce,N as ie,aE as L,aF as le,J as ue}from"../chunks/UXlYzocm.js";import{a as fe,m as de,u as me,s as he}from"../chunks/AD_Ai2mn.js";import"../chunks/DsnmJJEf.js";import{o as _e}from"../chunks/D8RLQ4bx.js";import{i as S}from"../chunks/DLbaRTQ9.js";import{p as A,b as C}from"../chunks/COKU4n9m.js";function T(l,e,a){N&&G();var c=l,n,r,t=null,s=null;function h(){r&&(z(r),r=null),t&&(t.lastChild.remove(),c.before(t),t=null),r=s,s=null}U(()=>{if(n!==(n=e())){var _=Q();if(n){var i=c;_&&(t=document.createDocumentFragment(),t.append(i=Y())),s=J(()=>a(i,n))}_?K.add_callback(h):h()}},W),N&&(c=X)}function ve(l){return class extends ge{constructor(e){super({component:l,...e})}}}class ge{#t;#e;constructor(e){var a=new Map,c=(r,t)=>{var s=ee(t,!1,!1);return a.set(r,s),s};const n=new Proxy({...e.props||{},$$events:{}},{get(r,t){return m(a.get(t)??c(t,Reflect.get(r,t)))},has(r,t){return t===H?!0:(m(a.get(t)??c(t,Reflect.get(r,t))),Reflect.has(r,t))},set(r,t,s){return k(a.get(t)??c(t,s),s),Reflect.set(r,t,s)}});this.#e=(e.hydrate?fe:de)(e.component,{target:e.target,anchor:e.anchor,props:n,context:e.context,intro:e.intro??!1,recover:e.recover}),(!e?.props?.$$host||e.sync===!1)&&Z(),this.#t=n.$$events;for(const r of Object.keys(this.#e))r==="$set"||r==="$destroy"||r==="$on"||$(this,r,{get(){return this.#e[r]},set(t){this.#e[r]=t},enumerable:!0});this.#e.$set=r=>{Object.assign(n,r)},this.#e.$destroy=()=>{me(this.#e)}}$set(e){this.#e.$set(e)}$on(e,a){this.#t[e]=this.#t[e]||[];const c=(...n)=>a.call(this,...n);return this.#t[e].push(c),()=>{this.#t[e]=this.#t[e].filter(n=>n!==c)}}$destroy(){this.#e.$destroy()}}const ye="modulepreload",be=function(l,e){return new URL(l,e).href},F={},w=function(e,a,c){let n=Promise.resolve();if(a&&a.length>0){let _=function(i){return Promise.all(i.map(d=>Promise.resolve(d).then(v=>({status:"fulfilled",value:v}),v=>({status:"rejected",reason:v}))))};const t=document.getElementsByTagName("link"),s=document.querySelector("meta[property=csp-nonce]"),h=s?.nonce||s?.getAttribute("nonce");n=_(a.map(i=>{if(i=be(i,c),i in F)return;F[i]=!0;const d=i.endsWith(".css"),v=d?'[rel="stylesheet"]':"";if(!!c)for(let o=t.length-1;o>=0;o--){const u=t[o];if(u.href===i&&(!d||u.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${i}"]${v}`))return;const f=document.createElement("link");if(f.rel=d?"stylesheet":ye,d||(f.as="script"),f.crossOrigin="",f.href=i,h&&f.setAttribute("nonce",h),document.head.appendChild(f),d)return new Promise((o,u)=>{f.addEventListener("load",o),f.addEventListener("error",()=>u(new Error(`Unable to preload CSS for ${i}`)))})}))}function r(t){const s=new Event("vite:preloadError",{cancelable:!0});if(s.payload=t,window.dispatchEvent(s),!s.defaultPrevented)throw t}return n.then(t=>{for(const s of t||[])s.status==="rejected"&&r(s.reason);return e().catch(r)})},Te={};var Ee=I('<div id="svelte-announcer" aria-live="assertive" aria-atomic="true" style="position: absolute; left: 0; top: 0; clip: rect(0 0 0 0); clip-path: inset(50%); overflow: hidden; white-space: nowrap; width: 1px; height: 1px"><!></div>'),Pe=I("<!> <!>",1);function pe(l,e){te(e,!0);let a=A(e,"components",23,()=>[]),c=A(e,"data_0",3,null),n=A(e,"data_1",3,null);re(()=>e.stores.page.set(e.page)),se(()=>{e.stores,e.page,e.constructors,a(),e.form,c(),n(),e.stores.page.notify()});let r=x(!1),t=x(!1),s=x(null);_e(()=>{const o=e.stores.page.subscribe(()=>{m(r)&&(k(t,!0),ae().then(()=>{k(s,document.title||"untitled page",!0)}))});return k(r,!0),o});const h=L(()=>e.constructors[1]);var _=Pe(),i=R(_);{var d=o=>{var u=O();const E=L(()=>e.constructors[0]);var P=R(u);T(P,()=>m(E),(g,y)=>{C(y(g,{get data(){return c()},get form(){return e.form},get params(){return e.page.params},children:(p,ke)=>{var D=O(),B=R(D);T(B,()=>m(h),(M,V)=>{C(V(M,{get data(){return n()},get form(){return e.form},get params(){return e.page.params}}),q=>a()[1]=q,()=>a()?.[1])}),b(p,D)},$$slots:{default:!0}}),p=>a()[0]=p,()=>a()?.[0])}),b(o,u)},v=o=>{var u=O();const E=L(()=>e.constructors[0]);var P=R(u);T(P,()=>m(E),(g,y)=>{C(y(g,{get data(){return c()},get form(){return e.form},get params(){return e.page.params}}),p=>a()[0]=p,()=>a()?.[0])}),b(o,u)};S(i,o=>{e.constructors[1]?o(d):o(v,!1)})}var j=ne(i,2);{var f=o=>{var u=Ee(),E=ce(u);{var P=g=>{var y=le();ue(()=>he(y,m(s))),b(g,y)};S(E,g=>{m(t)&&g(P)})}ie(u),b(o,u)};S(j,o=>{m(r)&&o(f)})}b(l,_),oe()}const je=ve(pe),De=[()=>w(()=>import("../nodes/0.B4C_3hdl.js"),__vite__mapDeps([0,1,2,3,4]),import.meta.url),()=>w(()=>import("../nodes/1.um0fz6Fw.js"),__vite__mapDeps([5,1,6,2,7,8,9,10,3]),import.meta.url),()=>w(()=>import("../nodes/2.CMUzneym.js"),__vite__mapDeps([11,1,6,2,7,12,13,14]),import.meta.url),()=>w(()=>import("../nodes/3.uFkFLWck.js"),__vite__mapDeps([15,1,2,7,12,16,10,3,8,9,13,17]),import.meta.url)],Ne=[],Fe={"/":[2],"/game/[id]":[3]},Re={handleError:({error:l})=>{console.error(l)},reroute:()=>{},transport:{}},we=Object.fromEntries(Object.entries(Re.transport).map(([l,e])=>[l,e.decode])),Ie=!1,Be=(l,e)=>we[l](e);export{Be as decode,we as decoders,Fe as dictionary,Ie as hash,Re as hooks,Te as matchers,De as nodes,je as root,Ne as server_loads};
